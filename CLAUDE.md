# wendao101 - Educational Platform Microservices

This is a Spring Cloud microservices project for an online education platform called "wendao101" (问到).

## Project Structure

- **wendao101-auth**: Authentication and authorization service
- **wendao101-gateway**: API Gateway service
- **wendao101-modules**: Core business modules
  - wendao101-douyin: <PERSON><PERSON>in (TikTok) integration module
  - wendao101-gen: Code generation module
  - wendao101-job: Scheduled job module
  - wendao101-ktma: KTMA module
  - wendao101-liteflow: LiteFlow rule engine module
  - wendao101-order: Order management module
  - wendao101-system: System management module
  - wendao101-teacher: Teacher management module
- **wendao101-api**: API definitions and interfaces
- **wendao101-common**: Shared common components

## Technology Stack

- **Java**: 1.8
- **Spring Boot**: 2.7.13
- **Spring Cloud**: 2021.0.8
- **Spring Cloud Alibaba**: 2021.0.5.0
- **MyBatis Plus**: 3.5.3.2
- **Nacos**: Service discovery and configuration
- **Database**: MySQL with Druid connection pool
- **Cache**: Redis
- **Message Queue**: RabbitMQ (Spring AMQP)

## Key Features

- Course management and live streaming
- Order processing and payment integration
- Teacher and student management
- Multi-platform integration (Douyin, WeChat, etc.)
- Video processing (Alibaba Cloud VOD, Tencent Cloud)
- SMS notifications
- Promoter/affiliate system

## Environment Profiles

- **dev**: Development (default) - Nacos: **************:8848
- **test**: Testing - Nacos: **************:8848
- **prod**: Production - Nacos: ************:8848

## Build Commands

```bash
# Build all modules
mvn clean compile

# Package all modules
mvn clean package

# Skip tests during build
mvn clean package -DskipTests
```

## Development Notes

- Tests are skipped by default in the Maven configuration
- Uses annotation processors for Lombok and MapStruct
- Resources are filtered during build for environment-specific configuration
- Docker support available in the docker/ directory