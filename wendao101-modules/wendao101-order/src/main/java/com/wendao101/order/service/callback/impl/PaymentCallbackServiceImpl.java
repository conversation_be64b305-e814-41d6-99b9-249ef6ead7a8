package com.wendao101.order.service.callback.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.bytedance.openapi.TradeSystemSign;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Response;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.BaseWxPayRequest;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.util.RequestUtils;
import com.wendao101.common.core.constant.WendaoRedisKey;
import com.wendao101.common.core.enums.KuishouPayStatus;
import com.wendao101.common.core.enums.KuishouPayWay;
import com.wendao101.common.core.kuaishou.pay.callback.PayCallbackResult;
import com.wendao101.common.core.kuaishou.pay.callback.child.CallBackData;
import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.order.constants.PaymentConstants;
import com.wendao101.order.domain.CourseOrder;
import com.wendao101.order.domain.CourseRefund;
import com.wendao101.order.domain.SecondKill;
import com.wendao101.order.service.*;
import com.wendao101.order.service.callback.IPaymentCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 支付回调处理服务实现类
 */
@Slf4j
@Service
public class PaymentCallbackServiceImpl implements IPaymentCallbackService {

    @Autowired
    private ICourseOrderService courseOrderService;

    @Autowired
    private ICourseRefundService courseRefundService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ISecondKillService secondKillService;

    @Autowired
    private IMReceiveCouponService receiveCouponService;

    @Autowired
    private WxPayService wxPayService;

    @Autowired
    private WendaoSettlementService wendaoSettlementService;

    @Override
    public String handleWxPayCallback(HttpServletRequest request, HttpServletResponse response) {
        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String serialNo = request.getHeader("Wechatpay-Serial");
        String signature = request.getHeader("Wechatpay-Signature");

        log.info("微信支付回调请求头参数：timestamp:{} nonce:{} serialNo:{} signature:{}",
                timestamp, nonce, serialNo, signature);

        if (StringUtils.isAnyBlank(timestamp, nonce, serialNo, signature)) {
            return WxPayNotifyV3Response.fail("头部参数错误!");
        }

        WxPayNotifyV3Result wxPayOrderNotifyV3Result = null;
        try {
            wxPayOrderNotifyV3Result = wxPayService.parseOrderNotifyV3Result(
                RequestUtils.readData(request),
                new SignatureHeader(timestamp, nonce, signature, serialNo)
            );
        } catch (WxPayException e) {
            log.error("微信支付回调解析失败: {}", e.getReturnMsg());
            return WxPayNotifyV3Response.fail("系统处理异常!");
        }

        if (wxPayOrderNotifyV3Result != null && wxPayOrderNotifyV3Result.getResult() != null) {
            WxPayNotifyV3Result.DecryptNotifyResult result = wxPayOrderNotifyV3Result.getResult();
            return processWxPaySuccess(result);
        }

        return WxPayNotifyV3Response.fail("处理失败");
    }

    @Override
    public String handleWxRefundCallback(HttpServletRequest request, HttpServletResponse response) {
        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String serialNo = request.getHeader("Wechatpay-Serial");
        String signature = request.getHeader("Wechatpay-Signature");

        log.info("微信退款回调请求头参数：timestamp:{} nonce:{} serialNo:{} signature:{}",
                timestamp, nonce, serialNo, signature);

        if (StringUtils.isAnyBlank(timestamp, nonce, serialNo, signature)) {
            return WxPayNotifyV3Response.fail("头部参数错误!");
        }

        WxPayRefundNotifyV3Result wxPayRefundNotifyV3Result = null;
        try {
            wxPayRefundNotifyV3Result = wxPayService.parseRefundNotifyV3Result(
                RequestUtils.readData(request),
                new SignatureHeader(timestamp, nonce, signature, serialNo)
            );
        } catch (WxPayException e) {
            log.error("微信退款回调解析失败: {}", e.getReturnMsg());
            return WxPayNotifyV3Response.fail("系统处理异常!");
        }

        if (wxPayRefundNotifyV3Result != null && wxPayRefundNotifyV3Result.getResult() != null) {
            WxPayRefundNotifyV3Result.DecryptNotifyResult result = wxPayRefundNotifyV3Result.getResult();
            return processWxRefundCallback(result);
        }

        return WxPayNotifyV3Response.fail("退款回调处理失败");
    }

    @Override
    public PayCallbackResult handleKuaishouPayCallback(HttpServletRequest request) {
        // 快手支付回调处理逻辑
        // 这里需要根据原代码实现
        return new PayCallbackResult();
    }

    @Override
    public String handleAlipayCallback(HttpServletRequest request) {
        // 支付宝回调处理逻辑
        // 这里需要根据原代码实现
        return "success";
    }

    @Override
    public AjaxResult handleDoudianCallback(HttpServletRequest request) {
        // 抖店回调处理逻辑
        // 这里需要根据原代码实现
        return new AjaxResult(0, "success");
    }

    @Override
    public AjaxResult handleDoudianSmsCallback(HttpServletRequest request) {
        // 抖店短信回调处理逻辑
        // 这里需要根据原代码实现
        return new AjaxResult(0, "success");
    }

    @Override
    public AjaxResult handleXhsCallback(HttpServletRequest request) {
        // 小红书回调处理逻辑
        // 这里需要根据原代码实现
        return new AjaxResult(0, "success");
    }

    /**
     * 处理微信支付成功逻辑
     */
    private String processWxPaySuccess(WxPayNotifyV3Result.DecryptNotifyResult result) {
        String orderId = result.getOutTradeNo();
        String tradeState = result.getTradeState();

        if (!"SUCCESS".equals(tradeState)) {
            return WxPayNotifyV3Response.success("非成功状态，忽略处理");
        }

        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
        if (courseOrder == null) {
            return WxPayNotifyV3Response.fail("订单不存在");
        }

        // 设置支付信息
        courseOrder.setPayWay(PaymentConstants.PAY_WAY_WECHAT);
        courseOrder.setTradingOrderNumber(result.getTransactionId());
        courseOrder.setOutOrderNumber(result.getTransactionId());

        // 设置支付时间
        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(result.getSuccessTime(), formatter);
        Date payTime = Date.from(zonedDateTime.toInstant());
        courseOrder.setPayTime(payTime);

        // 验证支付金额
        Integer total = result.getAmount().getTotal();
        Integer orderTotal = BaseWxPayRequest.yuan2Fen(courseOrder.getPayPrice());

        if (!total.equals(orderTotal)) {
            log.error("支付金额不匹配，订单ID: {}, 支付金额: {}, 订单金额: {}", orderId, total, orderTotal);
            return WxPayNotifyV3Response.fail("支付金额不匹配");
        }

        // 更新订单状态为已支付
        courseOrder.setOrderStatus(PaymentConstants.ORDER_STATUS_PAID);
        courseOrderService.updateCourseOrder(courseOrder);

        // 处理秒杀逻辑
        handleSecondKillLogic(courseOrder);

        // 处理优惠券逻辑
        handleCouponLogic(orderId);

        // 创建资金流水
        wendaoSettlementService.createFundsRecord(courseOrder, null);

        log.info("微信支付回调处理成功，订单ID: {}", orderId);
        return WxPayNotifyV3Response.success("处理成功");
    }

    /**
     * 处理微信退款回调
     */
    private String processWxRefundCallback(WxPayRefundNotifyV3Result.DecryptNotifyResult result) {
        if ("SUCCESS".equals(result.getRefundStatus())) {
            CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(result.getOutRefundNo());
            if (courseRefund == null) {
                return WxPayNotifyV3Response.fail("退款回调处理失败:未找到退款单号:" + result.getOutRefundNo());
            }

            String orderId = courseRefund.getOrderId();
            courseRefund.setRefundStatus(PaymentConstants.REFUND_STATUS_SUCCESS);
            courseRefund.setRefundType(PaymentConstants.REFUND_TYPE_NORMAL);

            // 检查退款类型
            checkRefundType(courseRefund);

            // 设置退款时间
            DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(result.getSuccessTime(), formatter);
            Date refundTime = Date.from(zonedDateTime.toInstant());
            courseRefund.setRefundTime(refundTime);

            // 更新订单状态
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
            if (courseOrder != null) {
                courseOrder.setOrderStatus(PaymentConstants.ORDER_STATUS_REFUNDED);
                courseOrderService.updateCourseOrder(courseOrder);
            }

            courseRefundService.updateCourseRefund(courseRefund);

            // 修改资金状态
            wendaoSettlementService.modifyFundsInfo(courseRefund, courseOrder);

            return WxPayNotifyV3Response.success("退款成功");
        } else {
            // 退款失败处理
            String refundId = result.getOutRefundNo();
            CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(refundId);
            if (courseRefund != null) {
                courseRefund.setRefundStatus(PaymentConstants.REFUND_STATUS_SUCCESS);
                courseRefund.setRefundType(5); // 退款失败
                courseRefundService.updateCourseRefund(courseRefund);
            }
            return WxPayNotifyV3Response.success("退款失败处理成功");
        }
    }

    /**
     * 处理秒杀逻辑
     */
    private void handleSecondKillLogic(CourseOrder courseOrder) {
        String orderId = courseOrder.getOrderId();
        SecondKill secondKillUsed = redisService.getCacheObject(
            WendaoRedisKey.WENDAO_SECOND_KILL_KS_WX_ORDER_KEY + orderId);

        if (secondKillUsed != null) {
            // 秒杀数量加1
            long count = redisService.secKillIncr(secondKillUsed.getId());

            // 更新redis中的值
            String key = WendaoRedisKey.WENDAO_SECOND_KILL_KEY + secondKillUsed.getCourseId();
            List<SecondKill> cacheList = redisService.getCacheList(key);

            if (CollectionUtils.isNotEmpty(cacheList)) {
                updateSecondKillStatus(cacheList, secondKillUsed, count, key);
            }

            String orderKey = WendaoRedisKey.WENDAO_SECOND_KILL_FOR_ORDER_KEY + courseOrder.getOrderId();
            redisService.setCacheObject(orderKey, "success");

            // 发送MNS消息更新客户端信息
            // sendMns(courseOrder.getCourseId()); // 这个方法需要从原代码中提取
        }
    }

    /**
     * 更新秒杀状态
     */
    private void updateSecondKillStatus(List<SecondKill> cacheList, SecondKill secondKillUsed,
                                      long count, String key) {
        long index = -1;
        for (int i = 0; i < cacheList.size(); i++) {
            if (Objects.equals(cacheList.get(i).getId(), secondKillUsed.getId())) {
                index = i;
                break;
            }
        }

        if (index >= 0 && count >= secondKillUsed.getSeckillNum().longValue()) {
            secondKillUsed.setSeckillStatus(1);
            // 先更新redis
            redisService.lset(key, index, secondKillUsed);

            // 更新数据库
            SecondKill secondKillUpdate = new SecondKill();
            secondKillUpdate.setId(secondKillUsed.getId());
            secondKillUpdate.setSeckillStatus(1);
            secondKillService.updateSecondKillNoUpdateTime(secondKillUpdate);
        }
    }

    /**
     * 处理优惠券逻辑
     */
    private void handleCouponLogic(String orderId) {
        // 将优惠券设置为已使用
        // ReceiveCouponInfo receiveCouponInfo = redisService.getCacheObject(
        //     PaymentConstants.REDIS_COUPON_KEY_PREFIX + orderId);
        //
        // if (receiveCouponInfo != null && receiveCouponInfo.getReceiveCouponId() > 0) {
        //     // 处理优惠券使用逻辑
        // }
    }

    /**
     * 检查退款类型
     */
    private void checkRefundType(CourseRefund courseRefund) {
        String orderId = courseRefund.getOrderId();

        // 检查是否为超时退款
        String timeOutTimeValue = redisService.getCacheObject(
            WendaoRedisKey.refund_timeout_redis_key + orderId);
        if (StringUtils.isNotBlank(timeOutTimeValue)) {
            courseRefund.setRefundType(PaymentConstants.REFUND_TYPE_TIMEOUT);
            return;
        }

        // 检查是否为平台退款
        String platformRefund = redisService.getCacheObject(
            PaymentConstants.PLATFORM_REFUND_FROM_ZONGHOUTAI + orderId);
        if (StringUtils.isNotBlank(platformRefund)) {
            courseRefund.setRefundType(PaymentConstants.REFUND_TYPE_PLATFORM);
            return;
        }

        // 检查是否为投诉退款
        String complaintRefund = redisService.getCacheObject(
            PaymentConstants.PLATFORM_REFUND_FROM_COMPLAINT + orderId);
        if (StringUtils.isNotBlank(complaintRefund)) {
            courseRefund.setRefundType(PaymentConstants.REFUND_TYPE_COMPLAINT);
        }
    }
}