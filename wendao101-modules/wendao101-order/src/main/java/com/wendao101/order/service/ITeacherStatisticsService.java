package com.wendao101.order.service;

import java.util.List;

import com.wendao101.common.core.web.page.TableDataInfoWithEmployeeDataSum;
import com.wendao101.common.core.web.page.TableDataInfoWithSumSaleMoney;
import com.wendao101.order.domain.EmployeeStatistics;
import com.wendao101.order.domain.SysDept;
import com.wendao101.order.domain.TeacherStatistics;

/**
 * 教师统计Service接口
 * 
 * <AUTHOR>
 */
public interface ITeacherStatisticsService {
    /**
     * 查询教师统计
     * 
     * @param id 教师统计主键
     * @return 教师统计
     */
    public TeacherStatistics selectTeacherStatisticsById(Long id);

    /**
     * 查询教师统计列表
     * 
     * @param teacherStatistics 教师统计
     * @return 教师统计集合
     */
    public List<TeacherStatistics> selectTeacherStatisticsList(TeacherStatistics teacherStatistics);

    /**
     * 新增教师统计
     * 
     * @param teacherStatistics 教师统计
     * @return 结果
     */
    public int insertTeacherStatistics(TeacherStatistics teacherStatistics);

    /**
     * 修改教师统计
     * 
     * @param teacherStatistics 教师统计
     * @return 结果
     */
    public int updateTeacherStatistics(TeacherStatistics teacherStatistics);

    /**
     * 批量删除教师统计
     * 
     * @param ids 需要删除的教师统计主键集合
     * @return 结果
     */
    public int deleteTeacherStatisticsByIds(Long[] ids);

    /**
     * 删除教师统计信息
     * 
     * @param id 教师统计主键
     * @return 结果
     */
    public int deleteTeacherStatisticsById(Long id);

    TableDataInfoWithSumSaleMoney selectTeacherStatisticsSum(TeacherStatistics teacherStatistics);

    List<Long> selectPromotersByTeacherId(Long teacherId);

    List<EmployeeStatistics> selectEmployeeStatisticsList(TeacherStatistics teacherStatistics);
    
    /**
     * 根据员工ID查询部门信息
     * @param employeeId 员工ID
     * @return 部门信息
     */
    SysDept selectDeptByEmployeeId(Long employeeId);
    
    /**
     * 根据部门ID查询上级部门信息
     * @param deptId 部门ID
     * @return 上级部门信息
     */
    SysDept selectParentDeptById(Long deptId);

    TableDataInfoWithEmployeeDataSum selectEmployeeStatisticsSum(TeacherStatistics teacherStatistics);
}