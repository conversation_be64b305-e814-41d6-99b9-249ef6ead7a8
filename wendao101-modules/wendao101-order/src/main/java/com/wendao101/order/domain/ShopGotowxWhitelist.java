package com.wendao101.order.domain;

import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ShopGotowxWhitelist extends BaseEntity {
    private static final long serialVersionUID = 1L;

    // 主键id
    private Long id;

    // 老师id
    private Long teacherId;

    // 老师手机号码
    private String mobile;

    // 店铺名称
    private String shopName;

    // 去微信小程序开启状态,1开启,2关闭
    private Integer wxMiniappOpenStatus;

    // 去抖音小程序开启状态,1开启,2关闭
    private Integer douyinMiniappOpenStatus;

    // 老师名称
    private String teacherName;

    // 来源的APP,1问到好课,2问到课堂
    private Integer appNameType;

    // 查询条件：老师信息
    private String teacherInfo;
}
