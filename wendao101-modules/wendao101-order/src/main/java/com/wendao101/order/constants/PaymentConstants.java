package com.wendao101.order.constants;

/**
 * 支付相关常量
 */
public class PaymentConstants {

    /**
     * Redis Key 前缀
     */
    public static final String REFUND_REASON_KEY_PREFIX = "REFUND_REASON_KEY_PREFIX";
    public static final String WX_PAY_USER_DATA = "WX_PAY_USER_DATA:";
    public static final String KS_WENDAO_KT_OR_ZK_ORDER_APPID = "KS_KT_ZK_ORDER_APPID:";
    public static final String ALIPAY_REDIRECTION_URL_KEY = "ALIPAY_REDIRECT_URL:";
    public static final String REDIS_COUPON_KEY_PREFIX = "ks_coupon_order:";

    /**
     * 抖店相关Redis Key
     */
    public static final String DOUDIAN_MATERIAL_UPLOAD_PREFIX = "doudian_material_upload_prefix:";
    public static final String DOUDIAN_SMS_SEND_PARAM_PREFIX = "doudian_sms_send_param_prefix:";
    public static final String DOUDIAN_SMS_SEND_SMS_ID_ORDER_ID_PREFIX = "doudian_sms_send_sms_id_order_id_prefix:";
    public static final String DOUDIAN_ORDER_COUPONS_REDIS_PREFIX = "doudian_order_coupons_redis_prefix:";
    public static final String DOUDIAN_RESEND_SMS_REDIS_PREFIX = "doudian_resend_sms_redis_prefix:";
    public static final String DOUDIAN_ORDER_COUPONS_REDIS_PREFIX_SHOP_ID = "doudian_order_coupons_redis_prefix_shop_id:";
    public static final String DOUDIAN_AFTERSALE_SHOP_ID_REDIS_PREFIX = "doudian_afterSale_shopId_redis_prefix:";

    /**
     * 订单相关Redis Key
     */
    public static final String DY_ORDER_CODE_URL = "dy_order_code_url:";
    public static final String KS_ORDER_CODE_URL = "ks_order_code_url:";
    public static final String GIVE_ORDER_ID_CACHE_LIST = "give_order_id_cache_list:";

    /**
     * 用户相关Redis Key
     */
    public static final String WX_H5_NOT_SYNC_USER_MOBILE = "wx_h5_not_sync_user_mobile:";
    public static final String APPLE_DY_SEND_SMS0_KEY_PREFIX = "apple_dy_send_sms0_key_prefix:";

    /**
     * 小红书相关Redis Key
     */
    public static final String XHS_ORDER_MAPPING_PREFIX = "xhs_order_mapping_prefix:";

    /**
     * 退款相关Redis Key
     */
    public static final String PLATFORM_REFUND_FROM_ZONGHOUTAI = "platform_refund_from_zonghoutai:";
    public static final String PLATFORM_REFUND_FROM_COMPLAINT = "platform_refund_from_Complaint:";

    /**
     * 支付方式常量
     */
    public static final String PAY_WAY_WECHAT = "微信支付";
    public static final String PAY_WAY_ALIPAY = "支付宝支付";
    public static final String PAY_WAY_KUAISHOU = "快手支付";
    public static final String PAY_WAY_DOUDIAN = "抖店支付";

    /**
     * 订单状态常量
     */
    public static final int ORDER_STATUS_UNPAID = 0;    // 未支付
    public static final int ORDER_STATUS_PAID = 1;      // 已支付
    public static final int ORDER_STATUS_REFUNDED = 2;  // 已退款

    /**
     * 退款状态常量
     */
    public static final int REFUND_STATUS_PENDING = 0;   // 退款中
    public static final int REFUND_STATUS_SUCCESS = 1;   // 退款成功
    public static final int REFUND_STATUS_FAILED = 2;    // 退款失败

    /**
     * 退款类型常量
     */
    public static final int REFUND_TYPE_NORMAL = 1;      // 正常退款
    public static final int REFUND_TYPE_TIMEOUT = 2;     // 超时退款
    public static final int REFUND_TYPE_PLATFORM = 6;    // 平台退款
    public static final int REFUND_TYPE_COMPLAINT = 9;   // 投诉退款

    /**
     * 平台标识常量
     */
    public static final int PLATFORM_WECHAT = 1;         // 微信平台
    public static final int PLATFORM_KUAISHOU = 2;       // 快手平台
    public static final int PLATFORM_ALIPAY = 8;         // 支付宝平台
    public static final int PLATFORM_DOUDIAN = 11;       // 抖店平台
    public static final int PLATFORM_XHS = 12;           // 小红书平台

    /**
     * 订单类型常量
     */
    public static final int ORDER_TYPE_VIRTUAL = 2;      // 虚拟商品订单
    public static final int ORDER_TYPE_PHYSICAL = 0;     // 实物商品订单
    public static final int ORDER_TYPE_POI = 4;          // POI核销订单
    public static final int ORDER_TYPE_THIRD_PARTY = 5;  // 三方核销订单
    public static final int ORDER_TYPE_SERVICE = 6;      // 服务市场订单

    private PaymentConstants() {
        // 私有构造函数，防止实例化
    }
}