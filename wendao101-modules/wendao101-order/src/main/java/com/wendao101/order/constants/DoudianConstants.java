package com.wendao101.order.constants;

/**
 * 抖店相关常量
 */
public class DoudianConstants {

    /**
     * 抖店消息标签常量
     */
    public static final String TAG_MATERIAL_AUDIT = "137";        // 素材上传状态变更消息
    public static final String TAG_PRODUCT_CHANGE = "400";        // 商品变更消息
    public static final String TAG_PRODUCT_INFO_CHANGE = "417";   // 商品信息变更消息
    public static final String TAG_ORDER_CREATE = "100";          // 订单创建
    public static final String TAG_ORDER_PAID = "101";            // 订单支付
    public static final String TAG_ORDER_SHIP = "102";            // 发货消息
    public static final String TAG_ORDER_COMPLETE = "103";        // 交易完成
    public static final String TAG_REFUND_CREATE = "200";         // 买家发起售后
    public static final String TAG_REFUND_AGREED = "201";         // 同意退款
    public static final String TAG_REFUND_CLOSED = "207";         // 售后关闭
    public static final String TAG_REFUND_SUCCESS = "206";        // 退款成功消息
    public static final String TAG_REFUND_REFUSED = "204";        // 拒绝退款消息
    public static final String TAG_RETURN_REFUSED = "205";        // 拒绝退货申请消息
    public static final String TAG_ADDRESS_CHANGE = "105";        // 买家收货信息变更消息

    /**
     * 抖店订单类型常量
     */
    public static final Long ORDER_TYPE_PHYSICAL = 0L;            // 实物订单
    public static final Long ORDER_TYPE_VIRTUAL = 2L;             // 虚拟商品订单
    public static final Long ORDER_TYPE_POI = 4L;                 // 电子券（poi核销）
    public static final Long ORDER_TYPE_THIRD_PARTY = 5L;         // 三方核销
    public static final Long ORDER_TYPE_SERVICE = 6L;             // 服务市场

    /**
     * 抖店支付方式常量
     */
    public static final Long PAY_TYPE_COD = 0L;                   // 货到付款
    public static final Long PAY_TYPE_WECHAT = 1L;                // 微信
    public static final Long PAY_TYPE_ALIPAY = 2L;                // 支付宝
    public static final Long PAY_TYPE_NO_PAYMENT = 7L;            // 无需支付（0元单）
    public static final Long PAY_TYPE_DOU_INSTALLMENT = 8L;       // DOU分期（信用支付）
    public static final Long PAY_TYPE_NEW_CARD = 9L;              // 新卡支付
    public static final Long PAY_TYPE_PAY_LATER = 12L;            // 先用后付
    public static final Long PAY_TYPE_CASHIER = 16L;              // 收银台支付

    /**
     * 抖店商品状态常量
     */
    public static final String PRODUCT_STATUS_ONLINE = "0";       // 商品上线
    public static final String PRODUCT_CHECK_STATUS_PASS = "3";   // 审核通过

    /**
     * 抖店素材审核状态常量
     */
    public static final int MATERIAL_AUDIT_PENDING = 1;           // 审核中
    public static final int MATERIAL_AUDIT_REJECTED = 2;          // 审核不通过
    public static final int MATERIAL_AUDIT_PASSED = 3;            // 审核通过

    /**
     * 抖店短信状态常量
     */
    public static final int SMS_STATUS_PENDING = 1;               // 发送中
    public static final int SMS_STATUS_FAILED = 2;                // 发送失败
    public static final int SMS_STATUS_SUCCESS = 3;               // 发送成功

    /**
     * 抖店推广员前缀
     */
    public static final String PROMOTER_PREFIX = "doudian_promoter_";

    /**
     * 抖店商品ID前缀
     */
    public static final String PRODUCT_ID_PREFIX_90000 = "90000";
    public static final String PRODUCT_ID_PREFIX_80000 = "80000";

    /**
     * 抖店回调响应常量
     */
    public static final int CALLBACK_SUCCESS_CODE = 0;
    public static final String CALLBACK_SUCCESS_MSG = "success";
    public static final int CALLBACK_FAIL_CODE = 1;
    public static final String CALLBACK_FAIL_MSG = "fail";

    /**
     * 抖店物流费用
     */
    public static final int LOGISTICS_FEE = 5;                    // 实物订单物流费用（元）

    private DoudianConstants() {
        // 私有构造函数，防止实例化
    }
}