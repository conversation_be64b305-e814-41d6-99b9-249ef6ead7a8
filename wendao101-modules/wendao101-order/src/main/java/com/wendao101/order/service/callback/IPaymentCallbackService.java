package com.wendao101.order.service.callback;

import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.kuaishou.pay.callback.PayCallbackResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 支付回调处理服务接口
 */
public interface IPaymentCallbackService {

    /**
     * 处理微信支付回调
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 处理结果
     */
    String handleWxPayCallback(HttpServletRequest request, HttpServletResponse response);

    /**
     * 处理微信退款回调
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 处理结果
     */
    String handleWxRefundCallback(HttpServletRequest request, HttpServletResponse response);

    /**
     * 处理快手支付回调
     *
     * @param request HTTP请求
     * @return 处理结果
     */
    PayCallbackResult handleKuaishouPayCallback(HttpServletRequest request);

    /**
     * 处理支付宝支付回调
     *
     * @param request HTTP请求
     * @return 处理结果
     */
    String handleAlipayCallback(HttpServletRequest request);

    /**
     * 处理抖店回调
     *
     * @param request HTTP请求
     * @return 处理结果
     */
    AjaxResult handleDoudianCallback(HttpServletRequest request);

    /**
     * 处理抖店短信回调
     *
     * @param request HTTP请求
     * @return 处理结果
     */
    AjaxResult handleDoudianSmsCallback(HttpServletRequest request);

    /**
     * 处理小红书回调
     *
     * @param request HTTP请求
     * @return 处理结果
     */
    AjaxResult handleXhsCallback(HttpServletRequest request);
}