package com.wendao101.order.mapper;

import com.wendao101.common.core.web.page.TableDataInfoWithEmployeeDataSum;
import com.wendao101.common.core.web.page.TableDataInfoWithSumSaleMoney;
import com.wendao101.order.domain.EmployeeStatistics;
import com.wendao101.order.domain.SysDept;
import com.wendao101.order.domain.TeacherStatistics;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教师统计Mapper接口
 * 
 * <AUTHOR>
 */
public interface TeacherStatisticsMapper 
{
    /**
     * 查询教师统计
     * 
     * @param id 教师统计主键
     * @return 教师统计
     */
    public TeacherStatistics selectTeacherStatisticsById(Long id);

    /**
     * 查询教师统计列表
     *
     * @param teacherStatistics 教师统计
     * @return 教师统计集合
     */
    public List<TeacherStatistics> selectTeacherStatisticsList(TeacherStatistics teacherStatistics);

    /**
     * 新增教师统计
     *
     * @param teacherStatistics 教师统计
     * @return 结果
     */
    public int insertTeacherStatistics(TeacherStatistics teacherStatistics);

    /**
     * 修改教师统计
     *
     * @param teacherStatistics 教师统计
     * @return 结果
     */
    public int updateTeacherStatistics(TeacherStatistics teacherStatistics);

    /**
     * 删除教师统计
     *
     * @param id 教师统计主键
     * @return 结果
     */
    public int deleteTeacherStatisticsById(Long id);

    /**
     * 批量删除教师统计
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTeacherStatisticsByIds(Long[] ids);

    TableDataInfoWithSumSaleMoney selectTeacherStatisticsSum(TeacherStatistics teacherStatistics);

    List<Long> selectPromotersByTeacherId(@Param("teacherId") Long teacherId);

    /**
     * 查询员工列表
     * 
     * @param teacherStatistics 查询条件
     * @return 员工列表
     */
    public List<EmployeeStatistics> selectEmployeeStatisticsList(TeacherStatistics teacherStatistics);
    
    /**
     * 根据员工ID查询部门信息
     * @param employeeId 员工ID
     * @return 部门信息
     */
    public SysDept selectDeptByEmployeeId(Long employeeId);
    
    /**
     * 根据部门ID查询上级部门信息
     * @param deptId 部门ID
     * @return 上级部门信息
     */
    public SysDept selectParentDeptById(Long deptId);

    /**
     * 查询员工统计数据
     * 
     * @param teacherStatistics 查询条件
     * @return 员工统计数据
     */
    public TableDataInfoWithEmployeeDataSum selectEmployeeStatisticsSum(TeacherStatistics teacherStatistics);
}