package com.wendao101.order.service.management;

import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.order.dto.CreateKuaishouPreOrderDTO;

import javax.servlet.http.HttpServletRequest;

/**
 * 订单管理服务接口
 */
public interface IOrderManagementService {

    /**
     * 创建预支付订单
     *
     * @param createKuaishouPreOrderDTO 订单创建参数
     * @param request HTTP请求
     * @return 预支付结果
     */
    AjaxResult createPreOrder(CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO, HttpServletRequest request);

    /**
     * 创建微信预支付订单
     *
     * @param createKuaishouPreOrderDTO 订单创建参数
     * @param request HTTP请求
     * @return 预支付结果
     */
    AjaxResult createWxPreOrder(CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO, HttpServletRequest request);

    /**
     * 处理订单支付成功后的业务逻辑
     *
     * @param orderId 订单ID
     * @return 处理结果
     */
    boolean processOrderPaymentSuccess(String orderId);

    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @param status 新状态
     * @return 更新结果
     */
    boolean updateOrderStatus(String orderId, Integer status);

    /**
     * 处理秒杀订单逻辑
     *
     * @param orderId 订单ID
     * @return 处理结果
     */
    boolean handleSecondKillOrder(String orderId);

    /**
     * 处理优惠券使用逻辑
     *
     * @param orderId 订单ID
     * @return 处理结果
     */
    boolean handleCouponUsage(String orderId);

    /**
     * 创建资金流水记录
     *
     * @param orderId 订单ID
     * @return 创建结果
     */
    boolean createFundRecord(String orderId);
}