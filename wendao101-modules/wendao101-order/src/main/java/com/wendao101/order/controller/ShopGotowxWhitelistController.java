package com.wendao101.order.controller;

import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.order.domain.ShopGotowxWhitelist;
import com.wendao101.order.service.IShopGotowxWhitelistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/shop_gotowx_whitelist_controller")
public class ShopGotowxWhitelistController extends BaseController {

    @Autowired
    private IShopGotowxWhitelistService shopGotowxWhitelistService;

    /**
     * 查询购课后去微信看课白名单
     *
     * @param id 主键id
     * @return 购课后去微信看课白名单
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(shopGotowxWhitelistService.selectShopGotowxWhitelistById(id));
    }

    /**
     * 查询购课后去微信看课白名单列表
     *
     * @param shopGotowxWhitelist 购课后去微信看课白名单
     * @return 购课后去微信看课白名单集合
     */
    @GetMapping("/list")
    public TableDataInfo selectShopGotowxWhitelistList(ShopGotowxWhitelist shopGotowxWhitelist) {
        startPage();
        List<ShopGotowxWhitelist> list = shopGotowxWhitelistService.selectShopGotowxWhitelistList(shopGotowxWhitelist);
        return getDataTable(list);
    }

    /**
     * 新增购课后去微信看课白名单
     *
     * @param shopGotowxWhitelist 购课后去微信看课白名单
     * @return 结果
     */
    @PostMapping
    public AjaxResult add(@RequestBody ShopGotowxWhitelist shopGotowxWhitelist) {
        if(shopGotowxWhitelist.getTeacherId()==null){
            return AjaxResult.error("老师id必传!");
        }
        ShopGotowxWhitelist result = shopGotowxWhitelistService.selectShopGotowxWhitelistByTeacherId(shopGotowxWhitelist.getTeacherId());
        if (result != null) {
            return AjaxResult.error("该老师已存在白名单,可以在列表中打开或关闭相关开关");
        }
        return toAjax(shopGotowxWhitelistService.insertShopGotowxWhitelist(shopGotowxWhitelist));
    }

    /**
     * 修改购课后去微信看课白名单
     *
     * @param shopGotowxWhitelist 购课后去微信看课白名单
     * @return 结果
     */
    @PutMapping
    public AjaxResult edit(@RequestBody ShopGotowxWhitelist shopGotowxWhitelist) {
        if(shopGotowxWhitelist.getId()==null){
            return AjaxResult.error("id必传!");
        }
        if(shopGotowxWhitelist.getTeacherId()==null){
            return AjaxResult.error("老师id必传!");
        }
        ShopGotowxWhitelist queryResult = shopGotowxWhitelistService.selectShopGotowxWhitelistById(shopGotowxWhitelist.getId());
        if(!Objects.equals(queryResult.getTeacherId(), shopGotowxWhitelist.getTeacherId())){
            return AjaxResult.error("老师id不允许修改!");
        }
        return toAjax(shopGotowxWhitelistService.updateShopGotowxWhitelist(shopGotowxWhitelist));
    }

    /**
     * 删除购课后去微信看课白名单
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(shopGotowxWhitelistService.deleteShopGotowxWhitelistByIds(ids));
    }
}
