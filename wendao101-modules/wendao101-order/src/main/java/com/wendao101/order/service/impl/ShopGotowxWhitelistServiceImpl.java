package com.wendao101.order.service.impl;

import com.wendao101.order.domain.ShopGotowxWhitelist;
import com.wendao101.order.mapper.ShopGotowxWhitelistMapper;
import com.wendao101.order.service.IShopGotowxWhitelistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ShopGotowxWhitelistServiceImpl implements IShopGotowxWhitelistService {

    @Autowired
    private ShopGotowxWhitelistMapper shopGotowxWhitelistMapper;

    @Override
    public ShopGotowxWhitelist selectShopGotowxWhitelistById(Long id) {
        return shopGotowxWhitelistMapper.selectShopGotowxWhitelistById(id);
    }

    @Override
    public List<ShopGotowxWhitelist> selectShopGotowxWhitelistList(ShopGotowxWhitelist shopGotowxWhitelist) {
        return shopGotowxWhitelistMapper.selectShopGotowxWhitelistList(shopGotowxWhitelist);
    }

    @Override
    public int insertShopGotowxWhitelist(ShopGotowxWhitelist shopGotowxWhitelist) {
        return shopGotowxWhitelistMapper.insertShopGotowxWhitelist(shopGotowxWhitelist);
    }

    @Override
    public int updateShopGotowxWhitelist(ShopGotowxWhitelist shopGotowxWhitelist) {
        return shopGotowxWhitelistMapper.updateShopGotowxWhitelist(shopGotowxWhitelist);
    }

    @Override
    public int deleteShopGotowxWhitelistById(Long id) {
        return shopGotowxWhitelistMapper.deleteShopGotowxWhitelistById(id);
    }

    @Override
    public int deleteShopGotowxWhitelistByIds(Long[] ids) {
        return shopGotowxWhitelistMapper.deleteShopGotowxWhitelistByIds(ids);
    }

    @Override
    public ShopGotowxWhitelist selectShopGotowxWhitelistByTeacherId(Long teacherId) {
        return shopGotowxWhitelistMapper.selectShopGotowxWhitelistByTeacherId(teacherId);
    }

    @Override
    public boolean isWxMiniappOpenByTeacherId(Long teacherId) {
        ShopGotowxWhitelist whitelist = shopGotowxWhitelistMapper.selectShopGotowxWhitelistByTeacherId(teacherId);
        return whitelist != null && whitelist.getWxMiniappOpenStatus() != null && whitelist.getWxMiniappOpenStatus() == 1;
    }

    @Override
    public boolean isDouyinMiniappOpenByTeacherId(Long teacherId) {
        ShopGotowxWhitelist whitelist = shopGotowxWhitelistMapper.selectShopGotowxWhitelistByTeacherId(teacherId);
        // 如果teacherId不在表中，返回true（相当于douyinMiniappOpenStatus为1）
        if (whitelist == null) {
            return true;
        }
        // 如果在表中，检查douyinMiniappOpenStatus是否为1
        return whitelist.getDouyinMiniappOpenStatus() != null && whitelist.getDouyinMiniappOpenStatus() == 1;
    }
}
