package com.wendao101.order.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.mns.client.CloudTopic;
import com.aliyun.mns.client.MNSClient;
import com.aliyun.mns.model.RawTopicMessage;
import com.aliyun.mns.model.TopicMessage;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import com.bytedance.openapi.TradeSystemSign;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailRequest;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailResponse;
import com.doudian.open.api.afterSale_Detail.data.AfterSaleDetailData;
import com.doudian.open.api.afterSale_Detail.param.AfterSaleDetailParam;
import com.doudian.open.api.coupons_list.CouponsListRequest;
import com.doudian.open.api.coupons_list.CouponsListResponse;
import com.doudian.open.api.coupons_list.data.DataItem;
import com.doudian.open.api.coupons_list.param.CouponsListParam;
import com.doudian.open.api.coupons_syncV2.CouponsSyncV2Request;
import com.doudian.open.api.coupons_syncV2.CouponsSyncV2Response;
import com.doudian.open.api.coupons_syncV2.param.CertListItem;
import com.doudian.open.api.coupons_syncV2.param.CouponsSyncV2Param;
import com.doudian.open.api.material_queryMaterialDetail.data.MaterialInfo;
import com.doudian.open.api.material_queryMaterialDetail.data.MaterialQueryMaterialDetailData;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailRequest;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailResponse;
import com.doudian.open.api.order_orderDetail.data.OrderOrderDetailData;
import com.doudian.open.api.order_orderDetail.data.SkuOrderListItem;
import com.doudian.open.api.order_orderDetail.param.OrderOrderDetailParam;
import com.doudian.open.api.product_detail.ProductDetailRequest;
import com.doudian.open.api.product_detail.ProductDetailResponse;
import com.doudian.open.api.product_detail.data.ProductDetailData;
import com.doudian.open.api.product_detail.data.SpecPricesItem;
import com.doudian.open.api.product_detail.param.ProductDetailParam;
import com.doudian.open.api.sms_send.SmsSendRequest;
import com.doudian.open.api.sms_send.SmsSendResponse;
import com.doudian.open.api.sms_send.param.SmsSendParam;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.msg.DoudianOpMsgParamRecord;
import com.doudian.open.core.msg.MsgParam;
import com.doudian.open.msg.material_auditResultForShop.MaterialAuditResultForShopRequest;
import com.doudian.open.msg.material_auditResultForShop.param.MaterialAuditResultForShopParam;
import com.doudian.open.msg.product_InfoChange.ProductInfoChangeRequest;
import com.doudian.open.msg.product_InfoChange.param.ProductInfoChangeParam;
import com.doudian.open.msg.product_InfoChange.param.Status;
import com.doudian.open.msg.product_change.ProductChangeRequest;
import com.doudian.open.msg.product_change.param.ProductChangeParam;
import com.doudian.open.msg.refund_RefundAgreed.RefundRefundAgreedRequest;
import com.doudian.open.msg.refund_RefundAgreed.param.RefundRefundAgreedParam;
import com.doudian.open.msg.refund_RefundClosed.RefundRefundClosedRequest;
import com.doudian.open.msg.refund_RefundClosed.param.RefundRefundClosedParam;
import com.doudian.open.msg.refund_RefundCreated.RefundRefundCreatedRequest;
import com.doudian.open.msg.refund_RefundCreated.param.RefundRefundCreatedParam;
import com.doudian.open.msg.refund_RefundRefused.RefundRefundRefusedRequest;
import com.doudian.open.msg.refund_RefundRefused.param.RefundRefundRefusedParam;
import com.doudian.open.msg.refund_RefundSuccess.RefundRefundSuccessRequest;
import com.doudian.open.msg.refund_RefundSuccess.param.RefundRefundSuccessParam;
import com.doudian.open.msg.refund_ReturnApplyRefused.RefundReturnApplyRefusedRequest;
import com.doudian.open.msg.refund_ReturnApplyRefused.param.RefundReturnApplyRefusedParam;
import com.doudian.open.msg.trade_TradeAddressChanged.TradeTradeAddressChangedRequest;
import com.doudian.open.msg.trade_TradeAddressChanged.param.TradeTradeAddressChangedParam;
import com.doudian.open.msg.trade_TradeCreate.TradeTradeCreateRequest;
import com.doudian.open.msg.trade_TradeCreate.param.TradeTradeCreateParam;
import com.doudian.open.msg.trade_TradePaid.TradeTradePaidRequest;
import com.doudian.open.msg.trade_TradePaid.param.TradeTradePaidParam;
import com.doudian.open.msg.trade_TradeSellerShip.TradeTradeSellerShipRequest;
import com.doudian.open.msg.trade_TradeSellerShip.param.TradeTradeSellerShipParam;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Response;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.BaseWxPayRequest;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.util.RequestUtils;
import com.google.gson.Gson;
import com.qcloud.vod.VodUploadClient;
import com.qcloud.vod.model.VodUploadRequest;
import com.qcloud.vod.model.VodUploadResponse;
import com.wendao101.common.core.constant.WendaoRedisKey;
import com.wendao101.common.core.doudian.AfterSaleReasons;
import com.wendao101.common.core.doudian.AfterSaleReasonsNeedHandle;
import com.wendao101.common.core.doudian.DoudianOrderSkuDTO;
import com.wendao101.common.core.douyin.generatelink.GenerateUrlLinkDTO;
import com.wendao101.common.core.douyin.generatelink.GenerateUrlLinkResult;
import com.wendao101.common.core.enums.KuishouPayStatus;
import com.wendao101.common.core.enums.KuishouPayWay;
import com.wendao101.common.core.kspaydto.*;
import com.wendao101.common.core.ktdto.GenerateUrlLinkRequestExt;
import com.wendao101.common.core.kuaishou.pay.*;
import com.wendao101.common.core.kuaishou.pay.callback.*;
import com.wendao101.common.core.kuaishou.pay.callback.child.CallBackData;
import com.wendao101.common.core.kuaishou.pay.callback.child.RefundData;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.utils.ip.IpUtils;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.wxmessage.RefundApplyMessage;
import com.wendao101.common.core.xhs.common.SkuOrderInfo;
import com.wendao101.common.core.xhs.common.XhsOrderSkuDTO;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.api.config.DouyinConfig;
import com.wendao101.douyin.api.config.KtKuaishouConfig;
import com.wendao101.douyin.api.config.KuaishouConfig;
import com.wendao101.douyin.api.config.ZkKuaishouConfig;
import com.wendao101.douyin.api.feign.KuaishouPayService;
import com.wendao101.douyin.api.feign.WxSendMessageService;
import com.wendao101.order.config.OrderAppConfig;
import com.wendao101.order.config.WendaoWxConfig;
import com.wendao101.order.constants.Appids;
import com.wendao101.order.domain.*;
import com.wendao101.order.dto.*;
import com.wendao101.order.feign.WxMaLinkService;
import com.wendao101.order.service.*;
import com.xiaohongshu.fls.opensdk.entity.afterSale.request.GetAfterSaleInfoRequest;
import com.xiaohongshu.fls.opensdk.entity.afterSale.response.GetAfterSaleInfoResponse;
import com.xiaohongshu.fls.opensdk.entity.data.DecryptedInfo;
import com.xiaohongshu.fls.opensdk.entity.data.request.BatchDecryptRequest;
import com.xiaohongshu.fls.opensdk.entity.order.Requset.GetOrderReceiverInfoRequest;
import com.xiaohongshu.fls.opensdk.entity.order.Requset.OrderDeliverRequest;
import com.xiaohongshu.fls.opensdk.entity.order.Response.GetOrderDetailResponse;
import com.xiaohongshu.fls.opensdk.entity.order.Response.OrderReceiverInfo;
import com.xiaohongshu.fls.opensdk.entity.product.request.v3.GetItemInfoRequest;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.GetItemInfoResponse;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.SkuDetail;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/wxkuaishou_pay_controller")
public class WxKuaishouPayController {
    private static final String REFUND_REASON_KEY_PREFIX = "REFUND_REASON_KEY_PREFIX";
    private static final String doudian_material_upload_prefix = "doudian_material_upload_prefix:";
    private static final String doudian_sms_send_param_prefix = "doudian_sms_send_param_prefix:";
    private static final String doudian_sms_send_sms_id_order_id_prefix = "doudian_sms_send_sms_id_order_id_prefix:";
    private static final String wx_h5_not_sync_user_mobile = "wx_h5_not_sync_user_mobile:";
    private static final String REDIS_COUPON_KEY_PREFIX = "ks_coupon_order:";
    private static final String WX_PAY_USER_DATA = "WX_PAY_USER_DATA:";
    private static final String KS_WENDAO_KT_OR_ZK_ORDER_APPID = "KS_KT_ZK_ORDER_APPID:";
    private static final String alipayRedirectionUrlKey = "ALIPAY_REDIRECT_URL:";
    private static final String dy_order_code_url = "dy_order_code_url:";
    private static final String ks_order_code_url = "ks_order_code_url:";
    private static final String apple_dy_send_sms0_key_prefix = "apple_dy_send_sms0_key_prefix:";
    private static final String xhs_order_mapping_prefix = "xhs_order_mapping_prefix:";
    private static final String PLATFORM_REFUND_FROM_ZONGHOUTAI = "platform_refund_from_zonghoutai:";
    private static final String PLATFORM_REFUND_FROM_Complaint = "platform_refund_from_Complaint:";
    private static final String give_order_id_cache_list = "give_order_id_cache_list:";
    private static final String doudian_order_coupons_redis_prefix = "doudian_order_coupons_redis_prefix:";
    private static final String doudian_resend_sms_redis_prefix = "doudian_resend_sms_redis_prefix:";
    private static final String doudian_order_coupons_redis_prefix_shop_id = "doudian_order_coupons_redis_prefix_shop_id:";
    private static final String doudian_afterSale_shopId_redis_prefix = "doudian_afterSale_shopId_redis_prefix:";
    @Autowired
    private IBuyCourseGiveOrderService buyCourseGiveOrderService;
    @Autowired
    private IDoudianShopConfigService doudianShopConfigService;
    @Autowired
    private ITeacherResourcePackageService teacherResourcePackageService;
    @Value("${wendao.video.vodsubAppid}")
    private Long vodsubAppid;
    @Value("${wendao.video.secretId}")
    private String secretId;
    @Value("${wendao.video.secretKey}")
    private String secretKey;
    @Autowired
    private KuaishouConfig kuaishouConfig;
    @Autowired
    private KtKuaishouConfig ktKuaishouConfig;
    @Autowired
    private ZkKuaishouConfig zkKuaishouConfig;
    @Autowired
    private KuaishouPayService kuaishouPayService;
    @Autowired
    private KsAccessTokenService ksAccessTokenService;
    @Autowired
    private ICourseOrderService courseOrderService;
    @Autowired
    private ICourseAuditService courseAuditService;
    @Autowired
    private IWendaoUserService wendaoUserService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IPromoterCourseService promoterCourseService;
    @Autowired
    private IPromoterService promoterService;
    @Autowired
    private IMReceiveCouponService mReceiveCouponService;
    @Autowired
    private IMReceiveCouponService receiveCouponService;
    @Autowired
    private ICourseRefundService courseRefundService;
    @Autowired
    private IMessageNotificationService messageNotificationService;
    @Autowired
    private IFundIncomeService fundIncomeService;
    @Autowired
    private ITeacherFlowRecordService teacherFlowRecordService;
    @Autowired
    private IPromoterFlowRecordService promoterFlowRecordService;
    @Autowired
    private IPromoterFundIncomeService promoterFundIncomeService;
    @Autowired
    private IWithdrawPriceService withdrawPriceService;
    @Autowired
    private IDYWenDaoUserService wenDaoUserService;
    @Autowired
    private WxPayService wxPayService;
    @Autowired
    private WendaoWxConfig wendaoWxConfig;
    @Autowired
    private IKsOrderReportService orderReportService;
    @Autowired
    ISecondKillService secondKillService;
    @Autowired
    private MNSClient mnsClient;
    @Autowired
    private AliPayKnowledgeStoreService aliPayService;
    @Autowired
    private DouyinConfig dyConfig;
    @Autowired
    private WxMaLinkService wxMaLinkService;
    @Autowired
    private IXhsCourseService xhsCourseService;
    @Autowired
    private XhsGoodsService xhsGoodsService;
    @Autowired
    private IDoudianCourseService doudianCourseService;
    @Autowired
    private IDoudianShippingOrderService doudianShippingOrderService;
    @Autowired
    private DoudianPriveteOrderService doudianPriveteOrderService;
    @Autowired
    private WxSendMessageService wxSendMessageService;
    @Autowired
    private DdHexiaoWhitelistService ddHexiaoWhitelistService;
    @Autowired
    private WendaoSettlementService wendaoSettlementService;

    @PostMapping("/dd_sms_callback")
    public AjaxResult ddSmsCallback(HttpServletRequest request) {
        //获取请求体
        String requestBody = null;
        try {
            requestBody = TradeSystemSign.getRequestBody(request);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        System.out.println("短信回执回调参数:" + requestBody);
        List<DdSmsCallBackDTO> smsCallBackList = JSON.parseArray(requestBody, DdSmsCallBackDTO.class);
        if (CollectionUtils.isEmpty(smsCallBackList)) {
            return new AjaxResult(0, "success");
        }
        for (DdSmsCallBackDTO dto : smsCallBackList) {
            if (StringUtils.isBlank(dto.getMessage_id())) {
                continue;
            }
            String orderId = redisService.getCacheObject(doudian_sms_send_sms_id_order_id_prefix + dto.getMessage_id());
            if (StringUtils.isBlank(orderId)) {
                continue;
            }
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
            if (courseOrder == null) {
                continue;
            }
            if (courseOrder.getDdSmsStatus() == 2 || courseOrder.getDdSmsStatus() == 3) {
                continue;
            }
            CourseOrder courseOrderUpdate = new CourseOrder();
            courseOrderUpdate.setId(courseOrder.getId());
            if ("0".equals(dto.getStatus_code())) {
                courseOrderUpdate.setDdSmsStatus(3);
            } else {
                courseOrderUpdate.setDdSmsStatus(2);
            }
            courseOrderService.updateCourseOrder(courseOrderUpdate);
        }
        return new AjaxResult(0, "success");
    }
    /**
     * 根据消息推送服务接入指南提供的信息，抖店开放平台对接收消息后的响应时间有严格要求。
     * 平台服务端响应的超时时间为2秒，建议开发者在接收到消息后立即返回成功的code（{"code":0,"msg":"success"}），
     * 再异步去处理自有的业务信息。如果超过2秒未返回成功code，
     * 系统会认为消息推送失败，并自动进行消息重推，最多重推3次，推送的时间间隔分别为30秒、5分钟和1小时。
     * @param request
     * @return
     */
    @PostMapping("/doudian_callback")
    public AjaxResult doudianCallback(HttpServletRequest request) {
        //获取请求体
        String requestBody = null;
        try {
            requestBody = TradeSystemSign.getRequestBody(request);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        System.out.println("接收到的抖店信息:" + requestBody);
        //获取http请求同中的event-sign,参数类型String
        String eventSign = request.getHeader("event-sign");
//        //获取http请求同中的app-id,参数类型String
        String appId = request.getHeader("app-id");
        List<JSONObject> jsonObjects = JSON.parseArray(requestBody, JSONObject.class);
        for (JSONObject json : jsonObjects) {
            if(json.getString("tag").equals("137")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //素材上传状态变更消息
                processMaterialChange(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("400")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //商品变更消息
                processProductChange(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("417")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //商品变更消息
                processProductInfoChange(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("100")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //订单创建
                processCreateOrder(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("101")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //订单支付,当创建和支付同时来的时候需要延迟
                boolean processResult = processOrderPay(appId, eventSign, JSON.toJSONString(list));
                if(!processResult){
                    //处理业务失败,等待抖店系统重发! 30秒后抖店会重发
                    return new AjaxResult(1,"fail");
                }
            }
            if(json.getString("tag").equals("102")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //发货消息
                //TradeTradeSellerShipParam
                processOrderShip(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("103")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //交易完成,结算金额到老师账户://TODO:
                //processOrderPay(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("200")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //买家发起售后
                processOrderRefund(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("201")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //同意退款
                processOrderRefundAgreed(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("207")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //售后关闭
                processOrderRefundClosed(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("206")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //退款成功消息
                processRefundSucceed(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("204")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //拒绝退款消息 //TODO:
                processRefundRefused(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("205")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //拒绝退货申请消息 //TODO:
                processReturnApplyRefused(appId, eventSign, JSON.toJSONString(list));
            }
            if(json.getString("tag").equals("105")){
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //买家收货信息变更消息 //TODO:
                processBuyerAddressChange(appId, eventSign, JSON.toJSONString(list));
            }
        }
        //处理业务逻辑
        return new AjaxResult(0,"success");
    }

    private void processOrderShip(String appId, String eventSign, String requestBody) {
        TradeTradeSellerShipRequest tradeTradeSellerShipRequest = new TradeTradeSellerShipRequest();
        MsgParam param = tradeTradeSellerShipRequest.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<TradeTradeSellerShipParam>> list = tradeTradeSellerShipRequest.getRequestBody();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (DoudianOpMsgParamRecord<TradeTradeSellerShipParam> item : list) {
            TradeTradeSellerShipParam data = item.getData();
            Long shopId = data.getShopId();
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(shopId);
            if(doudianShopConfig.getIsPublic()==0){
                continue;
            }
            //如果是实物订单,只执行主订单扣款5元,子订单不计算
            if (data.getOrderType() != null && data.getOrderType() == 0L) {
                String orderIdStr = String.valueOf(data.getPId());
                CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderIdStr);
                if (courseOrder == null) {
                    System.out.println("订单不存在，订单Id：" + orderIdStr);
                    //不存在!
                    continue;
                }
                // 扣除费用 插入一条扣款资金明细
                // 先查询订单是否已经有扣款记录,实物只扣除老师的金额
                FundIncome fundIncomeNew = new FundIncome();
                fundIncomeNew.setTeacherId(courseOrder.getTeacherId());
                fundIncomeNew.setOrderId(orderIdStr);
                fundIncomeNew.setIncomePlatform(11);
                List<FundIncome> fundIncomes = fundIncomeService.selectFundIncomeList(fundIncomeNew);
                if (CollectionUtils.isNotEmpty(fundIncomes)) {
                    FundIncome fundIncome = fundIncomes.get(0);
                    if (fundIncome.getRealIncomePrice() == null) {
                        fundIncome.setRealIncomePrice(BigDecimal.ZERO);
                    }
                    fundIncome.setLogisticsFee(new BigDecimal(5));
                    fundIncome.setRealIncomePrice(fundIncome.getRealIncomePrice().subtract(fundIncome.getLogisticsFee()));
                    fundIncomeService.updateFundIncome(fundIncome);
                }
            }
        }
    }

    private void processProductInfoChange(String appId, String eventSign, String requestBody) {
        ProductInfoChangeRequest productInfoChangeRequest = new ProductInfoChangeRequest();
        MsgParam param = productInfoChangeRequest.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<ProductInfoChangeParam>> list = productInfoChangeRequest.getRequestBody();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (DoudianOpMsgParamRecord<ProductInfoChangeParam> item : list) {
            ProductInfoChangeParam data = item.getData();
            Long productId = data.getProductId();
            //先查出商品
            DoudianCourse doudianCourse = doudianCourseService.selectDoudianCourseByProductId(productId);
            if (doudianCourse == null) {
                continue;
            }
            Long shopId = data.getShopId();
            if (shopId != null && shopId > 0L) {
                DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(shopId);
                if (doudianShopConfig.getIsPublic() != null && doudianShopConfig.getIsPublic() == 1) {
                    Status status = data.getStatus();
                    if (status != null) {
                        if ("0".equals(status.getStatus()) && "3".equals(status.getCheckStatus())) {
                            List<SpecPricesItem> specPricesItems = fetchSpecPrices(data.getShopId(), data.getProductId(), doudianCourse);
                            doudianPriveteOrderService.upChannelProduct(doudianCourse, specPricesItems,doudianShopConfig);
                            if (doudianCourse.getChannelId() != null && doudianCourse.getChannelId() > 0L) {
                                AccessToken accessToken = doudianPriveteOrderService.getAccessToken(doudianCourse.getShopId());
                                doudianPriveteOrderService.onlineChannelProductAsync(doudianCourse, doudianShopConfig, accessToken);
                            }
                        }
                    }
                }
            }
        }
    }

    private void processMaterialChange(String appId, String eventSign, String requestBody) {
        MaterialAuditResultForShopRequest request = new MaterialAuditResultForShopRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<MaterialAuditResultForShopParam>> list = request.getRequestBody();
        for (DoudianOpMsgParamRecord<MaterialAuditResultForShopParam> item : list) {
            MaterialAuditResultForShopParam data = item.getData();
            if (data == null || data.getAuditStatus() == null) {
                continue;
            }
            //获取审核状态
            Long auditStatus = data.getAuditStatus();
            MaterialQueryMaterialDetailData data1 = redisService.getCacheObject(doudian_material_upload_prefix + data.getMaterialId());
            if (data1 == null || data1.getMaterialInfo() == null) {
                continue;
            }
            MaterialInfo materialInfo = data1.getMaterialInfo();
            materialInfo.setAuditStatus(auditStatus.intValue());
            if (materialInfo.getAuditStatus() == 3) {
                materialInfo.setByteUrl(data.getByteUrl());
            } else {
                materialInfo.setAuditRejectDesc(data.getAuditStatusDesc());
            }
            data1.setMaterialInfo(materialInfo);
            redisService.setCacheObject(doudian_material_upload_prefix + data.getMaterialId(), data1, 1L, TimeUnit.DAYS);
        }
    }

    private void processBuyerAddressChange(String appId, String eventSign, String requestBody) {
        TradeTradeAddressChangedRequest request = new TradeTradeAddressChangedRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<TradeTradeAddressChangedParam>> list = request.getRequestBody();
        for(DoudianOpMsgParamRecord<TradeTradeAddressChangedParam> item:list){
            TradeTradeAddressChangedParam data = item.getData();
        }
    }

    private void processOrderRefundAgreed(String appId, String eventSign, String requestBody) {
        RefundRefundAgreedRequest request = new RefundRefundAgreedRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<RefundRefundAgreedParam>> list = request.getRequestBody();
        for(DoudianOpMsgParamRecord<RefundRefundAgreedParam> item:list){
            RefundRefundAgreedParam data = item.getData();
        }
    }

    private void processReturnApplyRefused(String appId, String eventSign, String requestBody) {
        RefundReturnApplyRefusedRequest request = new RefundReturnApplyRefusedRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<RefundReturnApplyRefusedParam>> list = request.getRequestBody();
        for(DoudianOpMsgParamRecord<RefundReturnApplyRefusedParam> item:list){
            RefundReturnApplyRefusedParam data = item.getData();
        }
    }

    private void processRefundRefused(String appId, String eventSign, String requestBody) {
        RefundRefundRefusedRequest request = new RefundRefundRefusedRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<RefundRefundRefusedParam>> list = request.getRequestBody();
        for(DoudianOpMsgParamRecord<RefundRefundRefusedParam> item:list){
            RefundRefundRefusedParam data = item.getData();
        }
    }

    private void processRefundSucceed(String appId, String eventSign, String requestBody) {
        RefundRefundSuccessRequest request = new RefundRefundSuccessRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<RefundRefundSuccessParam>> list = request.getRequestBody();
        for (DoudianOpMsgParamRecord<RefundRefundSuccessParam> item : list) {
            RefundRefundSuccessParam data = item.getData();
            //退款成功消息
            //售后Id
            String refundId = "" + data.getAftersaleId();
            System.out.println("退款成功");
            CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(refundId);
            if (courseRefund != null) {
                String orderId = courseRefund.getOrderId();
                courseRefund.setRefundStatus(1);
                courseRefund.setRefundType(1);
                CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
                if (courseOrder != null) {
                    Long shopId = data.getShopId();
                    DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(shopId);
                    System.out.println("订单不为空,设置订单为已退款");
                    courseOrder.setOrderStatus(2);
                    courseOrderService.updateCourseOrder(courseOrder);
                    courseRefundService.updateCourseRefund(courseRefund);
                    //退款成功 修改资金状态 备注信息
                    if(doudianShopConfig.getIsPublic()==1){
                        wendaoSettlementService.modifyFundsInfo(courseRefund, courseOrder);
                        //公共店铺有出版物记录的修改为退款
                        doudianPriveteOrderService.queryChuBanWuAndSetStatus(courseOrder);
                    }else{
                        doudianPriveteOrderService.modifyFundsInfo(courseRefund,courseOrder);
                    }
                }
            }

        }
    }

    private void processOrderRefundClosed(String appId, String eventSign, String requestBody) {
        RefundRefundClosedRequest request = new RefundRefundClosedRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<RefundRefundClosedParam>> list = request.getRequestBody();
        for(DoudianOpMsgParamRecord<RefundRefundClosedParam> item:list){
            RefundRefundClosedParam data = item.getData();
            Long afterSaleId = data.getAftersaleId();
            //删除售后单
            CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(""+afterSaleId);
            if(courseRefund!=null){
                courseRefundService.deleteCourseRefundById(courseRefund.getId());
            }
            //修改订单状态为已支付
            Long orderIdLong = data.getSId();
            if (orderIdLong == null) {
                orderIdLong = data.getPId();
            }
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId("" + orderIdLong);
            if(courseOrder!=null){
                courseOrder.setOrderStatus(1);
                courseOrderService.updateCourseOrder(courseOrder);
            }
        }
    }

    private void processOrderRefund(String appId, String eventSign, String requestBody) {
        System.out.println("用户发起售后!");
        RefundRefundCreatedRequest request = new RefundRefundCreatedRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<RefundRefundCreatedParam>> list = request.getRequestBody();
        for (DoudianOpMsgParamRecord<RefundRefundCreatedParam> item : list) {
            RefundRefundCreatedParam data = item.getData();
            //申请退款的金额（含运费）
            Long refundAmount = data.getRefundAmount();
            //售后单ID
            Long afterSaleId = data.getAftersaleId();
            Long orderIdLong = data.getSId();
            AccessToken accessToken = doudianPriveteOrderService.getAccessToken(data.getShopId());
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(data.getShopId());
            if (orderIdLong == null) {
                orderIdLong = data.getPId();
            }
            Long reasonCode = data.getReasonCode();
            //如果不是品质问题,则直接退款
            String reason = AfterSaleReasons.getReasonStr(reasonCode);
            String refundId = "" + afterSaleId;

            redisService.setCacheObject(doudian_afterSale_shopId_redis_prefix+refundId, data.getShopId(),7L, TimeUnit.DAYS);
            BigDecimal returnMoney = BaseWxPayRequest.fen2Yuan(new BigDecimal(refundAmount));
            //创建退款单
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId("" + orderIdLong);
            if(courseOrder==null){
                continue;
            }
            Long buyerUserId = courseOrder.getBuyerUserId();
            WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(buyerUserId);
            String openId = wendaoUser.getOpenId();
            CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(refundId);
            CreateWendaoRefundDTO createWendaoRefundDTO = new CreateWendaoRefundDTO();
            createWendaoRefundDTO.setOpenid(openId);
            createWendaoRefundDTO.setRefundReason(reason);
            createWendaoRefundDTO.setOrderId(courseOrder.getOrderId());
            //查询抖店数据
            AfterSaleDetailRequest afterSaleDetailRequest = new AfterSaleDetailRequest();
            afterSaleDetailRequest.setAppKey(doudianShopConfig.getAppKey());
            AfterSaleDetailParam afterSaleDetailParam = afterSaleDetailRequest.getParam();
            afterSaleDetailParam.setAfterSaleId(refundId);
            afterSaleDetailParam.setNeedOperationRecord(true);
            AfterSaleDetailResponse afterSaleDetailResponse = afterSaleDetailRequest.execute(accessToken);
            if(StringUtils.equals("10000",afterSaleDetailResponse.getCode())){
                AfterSaleDetailData afterSaleDetailData = afterSaleDetailResponse.getData();
                List<String> evidences = afterSaleDetailData.getProcessInfo().getAfterSaleInfo().getEvidence();
                if (CollectionUtils.isNotEmpty(evidences)) {
                    createWendaoRefundDTO.setReceiptImg(StringUtils.join(evidences, ","));
                }
                createWendaoRefund(createWendaoRefundDTO, refundId, courseRefund, returnMoney, 11);
            }
        }
    }

    private boolean processOrderPay(String appId, String eventSign, String requestBody) {
        TradeTradePaidRequest tradeTradePaidRequest = new TradeTradePaidRequest();
        MsgParam param = tradeTradePaidRequest.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);

        List<DoudianOpMsgParamRecord<TradeTradePaidParam>> list = tradeTradePaidRequest.getRequestBody();
        for(DoudianOpMsgParamRecord<TradeTradePaidParam> item:list){
            TradeTradePaidParam data = item.getData();
            //主订单
            Long pId = data.getPId();
            AccessToken accessToken = doudianPriveteOrderService.getAccessToken(data.getShopId());
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(data.getShopId());
            //按主订单查询订单信息
            OrderOrderDetailRequest orderOrderDetailRequest = new OrderOrderDetailRequest();
            orderOrderDetailRequest.setAppKey(doudianShopConfig.getAppKey());
            OrderOrderDetailParam orderOrderDetailParam = orderOrderDetailRequest.getParam();
            orderOrderDetailParam.setShopOrderId(String.valueOf(pId));
            OrderOrderDetailResponse orderOrderDetailResponse = orderOrderDetailRequest.execute(accessToken);
            OrderOrderDetailData orderOrderDetailData = orderOrderDetailResponse.getData();
            //子订单
            List<Long> sIds = data.getSIds();
            //pay_type
            //Int64
            //订单支付方式： 0: 货到付款 1: 微信 2: 支付宝
            Long payType = data.getPayType();
            //order_type
            //Int64
            //订单类型： 0: 实物 2: 普通虚拟 4: poi核销 5: 三方核销 6: 服务市场
            Long orderType = data.getOrderType();

            DoudianOrderSkuDTO doudianOrderSkuDTO = new DoudianOrderSkuDTO();
            doudianOrderSkuDTO.setOrderType(orderType);
            doudianOrderSkuDTO.setOrderId(String.valueOf(pId));
            doudianOrderSkuDTO.setTotalPayAmount(data.getPayAmount().intValue());

            //7=无需支付（0元单）；8=DOU分期（信用支付）；9=新卡支付；12=先用后付；16=收银台支付,订单支付方式： 0: 货到付款 1: 微信 2: 支付宝
            if(payType==0){
                doudianOrderSkuDTO.setPayWay("货到付款");
            }
            if(payType==1){
                doudianOrderSkuDTO.setPayWay("微信");
            }
            if(payType==2){
                doudianOrderSkuDTO.setPayWay("支付宝");
            }
            if(payType==7){
                doudianOrderSkuDTO.setPayWay("无需支付");
            }
            if(payType==8){
                doudianOrderSkuDTO.setPayWay("DOU分期");
            }
            if(payType==9){
                doudianOrderSkuDTO.setPayWay("新卡支付");
            }
            if(payType==12){
                doudianOrderSkuDTO.setPayWay("先用后付");
            }
            if(payType==16){
                doudianOrderSkuDTO.setPayWay("收银台支付");
            }
            doudianOrderSkuDTO.setSIds(sIds);
            AjaxResult ajaxResult = this.changeDoudianOrderToPaid(doudianOrderSkuDTO, orderOrderDetailData);
            if(ajaxResult.isError()){
                return false;
            }
        }
        return true;
    }

    private void processCreateOrder(String appId, String eventSign, String requestBody) {
        TradeTradeCreateRequest tradeTradeCreateRequest = new TradeTradeCreateRequest();
        MsgParam param0 = tradeTradeCreateRequest.getParam();
        param0.setAppId(appId);
        param0.setEventSign(eventSign);
        param0.setRequestBody(requestBody);

        List<DoudianOpMsgParamRecord<TradeTradeCreateParam>> list = tradeTradeCreateRequest.getRequestBody();
        for(DoudianOpMsgParamRecord<TradeTradeCreateParam> item:list){
            TradeTradeCreateParam data = item.getData();
            //订单创建
            Long pId = data.getPId();
            List<Long> sIds = data.getSIds();
            AccessToken accessToken = doudianPriveteOrderService.getAccessToken(data.getShopId());
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(data.getShopId());
            OrderOrderDetailRequest request = new OrderOrderDetailRequest();
            request.setAppKey(doudianShopConfig.getAppKey());
            OrderOrderDetailParam param = request.getParam();
            param.setShopOrderId(String.valueOf(pId));
            OrderOrderDetailResponse response = request.execute(accessToken);
            OrderOrderDetailData orderData = response.getData();
            //虚拟还是实物
            //boolean isVirtual = false;
            Long orderType = orderData.getShopOrderDetail().getOrderType();
            //0、普通订单 2、虚拟商品订单 4、电子券（poi核销） 5、三方核销 6、服务市场
            //总订单的订单金额
            Long order_amount  = orderData.getShopOrderDetail().getOrderAmount();
            //总订单的支付金额
            Long pay_amount  = orderData.getShopOrderDetail().getPayAmount();
            List<SkuOrderListItem> skuOrderList = orderData.getShopOrderDetail().getSkuOrderList();
            String doudianOpenId = orderData.getShopOrderDetail().getDoudianOpenId();

            //组装sku参数
            DoudianOrderSkuDTO doudianOrderSkuDTO = new DoudianOrderSkuDTO();
            doudianOrderSkuDTO.setOrderId(String.valueOf(pId));
            doudianOrderSkuDTO.setTotalPayAmount(order_amount.intValue());
            doudianOrderSkuDTO.setDoudianUserId(doudianOpenId);
            //实物订单还是虚拟订单
            doudianOrderSkuDTO.setOrderType(orderType);
            //支付方式,未支付之前不需要
            //额；7=无需支付（0元单）；8=DOU分期（信用支付）；9=新卡支付；12=先用后付；16=收银台支付
            //遍历
            List<SkuOrderInfo> skuInfoList = new ArrayList<>();
            Map<String,SkuOrderListItem> skuOrderListItemMap = new HashMap<>();
            for(SkuOrderListItem skuOrderListItem:skuOrderList){
                skuOrderListItemMap.put(skuOrderListItem.getOrderId(),skuOrderListItem);
                Long orderAmount = skuOrderListItem.getOrderAmount();
                Long payAmount = skuOrderListItem.getPayAmount();
                SkuOrderInfo skuOrderInfo = new SkuOrderInfo();
                skuOrderInfo.setOrderId(skuOrderListItem.getOrderId());
                skuOrderInfo.setSkuId(String.valueOf(skuOrderListItem.getSkuId()));
                skuOrderInfo.setTotalPaidAmount(orderAmount);
                skuOrderInfo.setAuthorId(skuOrderListItem.getAuthorId());
                //查询课程
                DoudianCourse doudianCourse = doudianCourseService.selectDoudianCourseByProductId(skuOrderListItem.getProductId());
                if (doudianCourse == null) {
                    System.out.println("skuId:" + skuOrderListItem.getSkuId() + "找不到对应的课程!");
                    continue;
                }
                if(doudianCourse.getMultiSku()==0){
                    String courseIdDoudian = String.valueOf(doudianCourse.getOutProductId());
                    if(courseIdDoudian.startsWith("90000")){
                        courseIdDoudian = courseIdDoudian.replace("90000","");
                    }
                    if(courseIdDoudian.startsWith("80000")){
                        courseIdDoudian = courseIdDoudian.replace("80000","");
                    }
                    skuOrderInfo.setCourseId(Long.parseLong(courseIdDoudian));
                }else{
                    //多sku
                    String outSkuId = skuOrderListItem.getOutSkuId();//这个字段前端作为课程id传入
                    if(StringUtils.isNotBlank(outSkuId)&&StringUtils.isNumeric(outSkuId)){
                        skuOrderInfo.setCourseId(Long.parseLong(outSkuId));
                    }else{
                        continue;
                    }
                }
                skuInfoList.add(skuOrderInfo);
            }
            doudianOrderSkuDTO.setSkuInfoList(skuInfoList);
            doudianOrderSkuDTO.setShopId(data.getShopId());
            //单sku商品,如果是多个sku则用户是使用购物车买的
            System.out.println("发送创建订单请求!!参数为:" + JSON.toJSONString(doudianOrderSkuDTO));
            this.createDoudianOrder(doudianOrderSkuDTO,skuOrderListItemMap);
        }
    }

    private void createDoudianOrder(DoudianOrderSkuDTO doudianOrderSkuDTO, Map<String,SkuOrderListItem> skuOrderListItemMap) {
        //调用创建订单
        System.out.println("创建抖店订单,入参:"+   JSON.toJSONString(doudianOrderSkuDTO));
        if (StringUtils.isBlank(doudianOrderSkuDTO.getOrderId()) || CollectionUtils.isEmpty(doudianOrderSkuDTO.getSkuInfoList())) {
            System.out.println("参数错误");
            return;
        }
        for (SkuOrderInfo skuOrderInfo : doudianOrderSkuDTO.getSkuInfoList()) {
            SkuOrderListItem skuOrderListItem = skuOrderListItemMap.get(skuOrderInfo.getOrderId());
            CourseOrder courseOrderExist = courseOrderService.selectCourseOrderByOrderId(skuOrderInfo.getOrderId());
            if (courseOrderExist != null) {
                System.out.println("订单已存在");
                return;
            }
            //创建多个订单
            CourseAudit courseAudit = courseAuditService.selectCourseAuditById(skuOrderInfo.getCourseId());
            if (courseAudit == null) {
                System.out.println("课程不存在");
                return;
            }
            //获取推广员信息
            String promoterOpenId = null;
            if (skuOrderInfo.getAuthorId() != null && skuOrderInfo.getAuthorId() > 0L) {
                System.out.println("此单为推广员订单或者老师自己播的");
                //查询是否推广员!
                String promoterPrefix = "doudian_promoter_";
                promoterOpenId = promoterPrefix + skuOrderInfo.getAuthorId();
            }
            Promoter finderPromoter = null;
            if(StringUtils.isNotBlank(promoterOpenId)){
                //查询推广员逻辑
                finderPromoter = fetchPromoterByOpenId(promoterOpenId, courseAudit);
            }
            Integer appNameType = courseAudit.getAppNameType();
            WendaoUser wendaoUser = wendaoUserService.selectWendaoUserByOpenIdPlatformAppNameType(doudianOrderSkuDTO.getDoudianUserId(), appNameType, 11);
            if (wendaoUser==null) {
                //创建用户
                wendaoUser = new WendaoUser();
                wendaoUser.setOpenId(doudianOrderSkuDTO.getDoudianUserId());
                wendaoUser.setPlatform(11);
                wendaoUser.setAppNameType(appNameType);
                wendaoUserService.insertWendaoUser(wendaoUser);
            }
            CourseOrder courseOrder = new CourseOrder();
            courseOrder.setOrderId(skuOrderInfo.getOrderId());
            Integer courseDuration = courseAudit.getCourseDuration();
            courseOrder.setCourseDuration(courseDuration == null ? 0L : courseDuration.longValue());
            courseOrder.setCourseId(courseAudit.getId());
            courseOrder.setTeacherId(courseAudit.getTeacherId());
            courseOrder.setCourseImgUrl(courseAudit.getCoverPicUrl());
            courseOrder.setValidity(courseAudit.getExpirationDay());
            courseOrder.setCourseTitle(skuOrderListItem.getProductName());
            BigDecimal price = BaseWxPayRequest.fen2Yuan(new BigDecimal(skuOrderInfo.getTotalPaidAmount()));
            courseOrder.setCoursePrice(price);
            courseOrder.setAppNameType(wendaoUser.getAppNameType());
            courseOrder.setPayPrice(price);
            courseOrder.setIsCourse(0);
            courseOrder.setOriginalPrice(courseAudit.getOriginalPrice());
            courseOrder.setOrderStatus(0);
            //以这个来判断是否是抖店订单!!!
            courseOrder.setOrderType(11);
            courseOrder.setOrderTime(new Date());
            courseOrder.setFundsType(0);
            courseOrder.setOrderPlatform(11);
            courseOrder.setBuyerUserId(wendaoUser.getId());
            //设置外部订单号为课程Id
            courseOrder.setOutOrderNumber(null);
            if(StringUtils.isNotBlank(skuOrderListItem.getOutProductId())){
                courseOrder.setOutOrderNumber(skuOrderListItem.getOutProductId());
            }
            courseOrder.setMyEarningsPrice(price);
            if(finderPromoter!=null){
                //推广员逻辑
                PromoterCourse promoterCourse = new PromoterCourse();
                promoterCourse.setCourseId(skuOrderInfo.getCourseId());
                promoterCourse.setPromoterId(finderPromoter.getId());
                List<PromoterCourse> promoterCourses = promoterCourseService.selectPromoterCourseList(promoterCourse);
                if (CollectionUtils.isNotEmpty(promoterCourses)) {
                    courseOrder.setIsPromoter(1);
                    courseOrder.setOrderType(1);
                    PromoterCourse promoterCourse1 = promoterCourses.get(0);
                    Promoter promoter = promoterService.selectPromoterById(finderPromoter.getId());
                    if (promoter != null) {
                        courseOrder.setPromoterId(finderPromoter.getId());
                        courseOrder.setPromoterMobile(promoter.getPromoterPhone());
                        courseOrder.setPromoterName(promoter.getPromoterName());
                        long spreadRate = promoterCourse1.getSpreadRate() == null ? 0L : promoterCourse1.getSpreadRate();
                        courseOrder.setPromotionRatio(Long.toString(spreadRate));
                        if ((int) spreadRate > 0) {
                            BigDecimal multiply = price.multiply(new BigDecimal(spreadRate));
                            BigDecimal divide = multiply.divide(new BigDecimal(100));
                            divide = divide.setScale(2, RoundingMode.DOWN);
                            courseOrder.setPromoterEarningsPrice(divide);
                            courseOrder.setMyEarningsPrice(price.subtract(divide));
                        }
                    }
                }
            }
            courseOrder.setDdShopId(doudianOrderSkuDTO.getShopId());
            courseOrderService.insertCourseOrder(courseOrder);
        }
    }

    private Promoter fetchPromoterByOpenId(String promoterOpenId, CourseAudit courseAudit) {
        if(StringUtils.isNotBlank(promoterOpenId)){
            WendaoUser wendaoUserPQ = new WendaoUser();
            wendaoUserPQ.setOpenId(promoterOpenId);
            wendaoUserPQ.setPlatform(11);
            wendaoUserPQ.setAppNameType(courseAudit.getAppNameType());
            List<WendaoUser> wendaoUsers = wendaoUserService.selectWendaoUserList(wendaoUserPQ);
            //如果用户存在
            if (CollectionUtils.isNotEmpty(wendaoUsers)) {
                //推广员用户
                WendaoUser wendaoUser = wendaoUsers.get(0);
                //是推广员
                Long id = wendaoUser.getId();
                String idString = String.valueOf(id);
                List<Promoter> promoters = promoterService.selectPromoterListTemp(courseAudit.getTeacherId(), idString);
                if (CollectionUtils.isNotEmpty(promoters)) {
                    for (Promoter p : promoters) {
                        String authorizedAccountList = p.getAuthorizedAccountList();
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(authorizedAccountList)) {
                            List<PromoterAuthorizedAccountDTO> authorizedAccounts = JSON.parseArray(authorizedAccountList, PromoterAuthorizedAccountDTO.class);
                            if (CollectionUtils.isNotEmpty(authorizedAccounts)) {
                                for (PromoterAuthorizedAccountDTO authorizedAccount : authorizedAccounts) {
                                    Long account = authorizedAccount.getAccount();
                                    Integer accountPlatform = authorizedAccount.getPlatform();
                                    if (Objects.equals(account, id) && accountPlatform != null && accountPlatform == 11 && authorizedAccount.getFlag()!=null && authorizedAccount.getFlag()) {
                                        return p;
                                    }
                                }
                            }
                        }
                    }
                } else {
                    System.out.println("没有符合的推广员数据!");
                }
            }
        }
        return null;
    }

    private AjaxResult changeDoudianOrderToPaid(DoudianOrderSkuDTO doudianOrderSkuDTO, OrderOrderDetailData orderOrderDetailData) {
        if (StringUtils.isBlank(doudianOrderSkuDTO.getOrderId())) {
            return AjaxResult.error("参数错误");
        }
        boolean isDouyinLite = orderOrderDetailData != null && orderOrderDetailData.getShopOrderDetail() != null
                && orderOrderDetailData.getShopOrderDetail().getBType() != null
                && orderOrderDetailData.getShopOrderDetail().getBType().intValue() == 11;//2是抖音
        List<SkuOrderListItem> skuOrderList = orderOrderDetailData.getShopOrderDetail().getSkuOrderList();
        Map<String, SkuOrderListItem> orderList = new HashMap<>();
        for (SkuOrderListItem skuOrderItem : skuOrderList) {
            orderList.put(skuOrderItem.getOrderId(), skuOrderItem);
        }
        for (Long orderId : doudianOrderSkuDTO.getSIds()) {
            String orderIdStr = String.valueOf(orderId);
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderIdStr);
            if (courseOrder == null) {
                return AjaxResult.error("订单还没有创建!");
            }
            if (courseOrder.getOrderStatus() == 1) {
                //已支付
                continue;
            }
            //获取抖店订单信息
            SkuOrderListItem skuOrderListItem = orderList.get(orderIdStr);
            if (skuOrderListItem == null) {
                continue;
            }
            BigDecimal price = BaseWxPayRequest.fen2Yuan(new BigDecimal(skuOrderListItem.getPayAmount()));
            courseOrder.setPayPrice(price);
            courseOrder.setMyEarningsPrice(price);
            courseOrder.setSettleStatus(1);
            courseOrder.setOrderStatus(1);
            courseOrder.setPayTime(new Date());
            long spreadRate = 0L;
            if(StringUtils.isNotBlank(courseOrder.getPromotionRatio())&&StringUtils.isNumeric(courseOrder.getPromotionRatio())){
                spreadRate = Long.parseLong(courseOrder.getPromotionRatio());
            }
            if ((int) spreadRate > 0) {
                BigDecimal multiply = price.multiply(new BigDecimal(spreadRate));
                BigDecimal divide = multiply.divide(new BigDecimal(100));
                divide = divide.setScale(2, RoundingMode.DOWN);
                courseOrder.setPromoterEarningsPrice(divide);
                courseOrder.setMyEarningsPrice(price.subtract(divide));
            }
            //支付方式
            if (StringUtils.isNotBlank(doudianOrderSkuDTO.getPayWay())) {
                courseOrder.setPayWay(doudianOrderSkuDTO.getPayWay());
            } else {
                courseOrder.setPayWay("抖音支付");
            }
            List<Long> noDyTeacherIds = new ArrayList<>();
            noDyTeacherIds.add(254126646L);
            noDyTeacherIds.add(254128357L);
            noDyTeacherIds.add(254126229L);
            noDyTeacherIds.add(254128299L);
            noDyTeacherIds.add(254128797L);
            noDyTeacherIds.add(254128809L);
            noDyTeacherIds.add(254128829L);
            noDyTeacherIds.add(254128743L);
            noDyTeacherIds.add(254128707L);
            courseOrder.setTradingOrderNumber(orderOrderDetailData.getShopOrderDetail().getChannelPaymentNo());
            courseOrderService.updateCourseOrder(courseOrder);
            //查询抖店配置
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(orderOrderDetailData.getShopOrderDetail().getShopId());
            //创建流水
            if(doudianShopConfig.getIsPublic()==1){
                wendaoSettlementService.createFundsRecord(courseOrder,skuOrderListItem);
            }else{
                //如果不是公共店铺则不创建流水，不对账户加金额
                doudianPriveteOrderService.createFundsRecord(courseOrder,doudianShopConfig);
            }
            String clientTokenKey = dyConfig.getClientAccessTokenKey() + ":" + courseOrder.getAppNameType();
            String clentAccessToken = redisService.getCacheObject(clientTokenKey);
            String key = "wxcodedoudian:" + orderIdStr;
            String link = redisService.getCacheObject(key);
            if (StringUtils.isBlank(link) && StringUtils.isNotBlank(clentAccessToken)&&!noDyTeacherIds.contains(courseOrder.getTeacherId())) {
                GenerateUrlLinkDTO generateUrlLinkDTO = new GenerateUrlLinkDTO();
                generateUrlLinkDTO.setApp_id(Appids.getAppidByAppNameType(courseOrder.getAppNameType()));
                if(isDouyinLite){
                    generateUrlLinkDTO.setApp_name("douyinlite");
                }else{
                    generateUrlLinkDTO.setApp_name("douyin");
                }
                //加160天
                long time = (new Date().getTime() + 160L * 24L * 60L * 60L * 1000L) / 1000;
                generateUrlLinkDTO.setExpire_time(time);
                // 创建一个 Map 来存储所有的参数
                Map<String, String> queryParams = new HashMap<>();
                queryParams.put("orderId", orderIdStr);
                queryParams.put("platform", "0");
                queryParams.put("appNameType", String.valueOf(courseOrder.getAppNameType()));
                queryParams.put("buyerUserId", String.valueOf(courseOrder.getBuyerUserId()));
                String jsonQuery = JSON.toJSONString(queryParams);
                generateUrlLinkDTO.setQuery(jsonQuery);
                generateUrlLinkDTO.setPath("pages_mine/bind_phone/bind_phone");
                GenerateUrlLinkResult generateUrlLinkResult = postRequest(generateUrlLinkDTO, "/api/apps/v1/url_link/generate/", GenerateUrlLinkResult.class, "https://open.douyin.com", clentAccessToken);
                if (generateUrlLinkResult != null && generateUrlLinkResult.getErr_no() == 0) {
                    link = generateUrlLinkResult.getData().getUrl_link();
                    redisService.setCacheObject(key, link, 160L, TimeUnit.DAYS);
                }
            }else if(StringUtils.isBlank(link) && noDyTeacherIds.contains(courseOrder.getTeacherId())){
                GenerateUrlLinkRequestExt dto = new GenerateUrlLinkRequestExt();
                dto.setAppId("wx0e5e01d239197bb1");
                dto.setPath("pages_mine/bind_phone/bind_phone");
                dto.setQuery("orderId=" + orderIdStr + "&platform=1&appNameType=" + "2");
                dto.setEnvVersion("release");
                dto.setExpireInterval(30);
                dto.setExpireType(1);
                dto.setIsExpire(true);
                AjaxResult ajaxResult = wxMaLinkService.generateUrlLink(dto);
                if (ajaxResult.isSuccess()) {
                    link = (String) ajaxResult.get("data");
                    redisService.setCacheObject(key, link, 30L, TimeUnit.DAYS);
                }
            }
            AccessToken accessToken = doudianPriveteOrderService.getAccessToken(orderOrderDetailData.getShopOrderDetail().getShopId());
            Long dShopId = orderOrderDetailData.getShopOrderDetail().getShopId();
            //从链接中获取最后一个/后面的字符串，私有店铺
            // https://z.douyin.com/xEcuol6
            boolean isLjcxcj = Objects.equals(dShopId, OrderAppConfig.ljcxcjIdLong);
            String urlCode = link.substring(link.lastIndexOf("/") + 1);
            String smsUrl = link.replace("https://","");;
            //私有店铺
            redisService.setCacheObject("doudian_Sms_Url_Code:" + urlCode, link, 160L, TimeUnit.DAYS);
            boolean isWendao = Objects.equals(dShopId, OrderAppConfig.wendaoShopIdLong);
            boolean isWending = Objects.equals(dShopId, OrderAppConfig.wendingShopIdLong);
            boolean isWnedaoNew = Objects.equals(dShopId, OrderAppConfig.wendaoNewShopIdLong);
            boolean isYgjjdp = Objects.equals(dShopId, OrderAppConfig.ygjydpIdLong);
            boolean isWBBShop = Objects.equals(dShopId, OrderAppConfig.WBB_SHOP_ID);
            boolean isWenDa = Objects.equals(dShopId, OrderAppConfig.WenDa);
            //发送短信链接
            SmsSendRequest request = new SmsSendRequest();
            request.setAppKey(doudianShopConfig.getAppKey());
            SmsSendParam param = request.getParam();
            param.setSmsAccount("7f07982f");
            if (isWendao) {
                param.setSign("问到课堂");
                param.setTemplateId("ST_7f954828");
            } else if (isWending) {
                param.setSign("问到课堂");
                param.setTemplateId("ST_7f95006a");
            } else if (isWnedaoNew) {
                param.setSign("问到课堂");
                param.setTemplateId("ST_7fe400ec");
            } else if (isLjcxcj) {
                param.setSmsAccount("801e36da");
                param.setSign("零基础学裁剪");
                param.setTemplateId("ST_801e8a4f");
            } else if (isYgjjdp) {
                param.setSmsAccount("804090b6");
                param.setSign("杨工教育店铺");
                param.setTemplateId("ST_8040b358");
            } else if (isWBBShop) {
                param.setSmsAccount("80729ee1");
                param.setSign("伍伯伯吉他铺");
                param.setTemplateId("ST_8072ccdd");
            } else if (isWenDa) {
                param.setSmsAccount("7f07982f");
                param.setSign("问到课堂");
                param.setTemplateId("ST_8284fd73");
            }
            String smsSendProductName = (StringUtils.isBlank(courseOrder.getCourseTitle()) ? "课程" : courseOrder.getCourseTitle());
            //如果smsSendProductName超过6个中文字则截取6个中文字,后面加三个点...,不超过则不截取
            // 判断字符串长度是否超过6个字符
            if (smsSendProductName.length() > 6) {
                // 截取前6个字符并添加省略号
                smsSendProductName = smsSendProductName.substring(0, 6) + "...";
                List<String> bandList = new ArrayList<>();
                bandList.add("短视频剪辑教...");
                bandList.add("财商新思维【...");
                bandList.add("唱歌声音测评...");
                if(bandList.contains(smsSendProductName)){
                    smsSendProductName = "课程";
                }
            }
            param.setTemplateParam("{\"productName\":\"" + smsSendProductName + "\",\"learnUrl\":\"" + smsUrl + "\"}");
            param.setPostTel(skuOrderListItem.getEncryptPostTel());

            SmsSendResponse response = request.execute(accessToken);
            System.out.println(com.alibaba.fastjson.JSON.toJSONString(response));
            /**
             * 发送短信记录短信id，稍后检查成功失败状态
             */
            if ("10000".equals(response.getCode())&&response.getData() != null) {
                courseOrder.setDdSmsStatus(1);
                courseOrder.setDdSmsId(response.getData().getMessageId());
                redisService.setCacheObject(doudian_sms_send_param_prefix+response.getData().getMessageId(),param, 35L, TimeUnit.DAYS);
                redisService.setCacheObject(doudian_sms_send_sms_id_order_id_prefix+response.getData().getMessageId(),courseOrder.getOrderId(), 3L, TimeUnit.DAYS);
                courseOrderService.updateCourseOrder(courseOrder);
            } else {
                courseOrder.setDdSmsStatus(2);
                log.error("发送短信失败，订单号：" + orderIdStr + "，原因：" + response.getMsg() + "，" + response.getSubMsg());
                courseOrderService.updateCourseOrder(courseOrder);
            }
            redisService.setCacheObject(doudian_resend_sms_redis_prefix+orderIdStr, urlCode, 29L, TimeUnit.DAYS);
            //设置队列
            try{
                if (isWendao || isWending || isWnedaoNew || isWenDa) {
                    if(!noDyTeacherIds.contains(courseOrder.getTeacherId())){
                        redisService.addOrderIdToQueue(dShopId, orderIdStr);
                    }
                }
            }catch(Exception e){
                log.error("redisService.addOrderIdToQueue(dShopId, orderIdStr)异常",e);
            }

            //判断是否虚拟商品
            Long orderType = skuOrderListItem.getOrderType();
            if (orderType != null && orderType == 5L) {
                CouponsSyncV2Request couponsSyncV2Request = new CouponsSyncV2Request();
                couponsSyncV2Request.setAppKey(doudianShopConfig.getAppKey());
                CouponsSyncV2Param couponsSyncV2Param = couponsSyncV2Request.getParam();
                //子订单Id
                couponsSyncV2Param.setOrderId(orderIdStr);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                CertListItem certListItem = new CertListItem();
                //领取地址
                String getCouponsUrl = "https://" + smsUrl;
                certListItem.setCertLink(getCouponsUrl);
                certListItem.setCertNo(urlCode);
                certListItem.setGrantTime(sdf.format(new Date()));
                couponsSyncV2Param.setCertList(Collections.singletonList(certListItem));
                CouponsSyncV2Response couponsSyncV2Response = couponsSyncV2Request.execute(accessToken);
                System.out.println("同步卡券结果:" + JSON.toJSONString(couponsSyncV2Response));
                if ("10000".equalsIgnoreCase(couponsSyncV2Response.getCode())) {
                    //存入redis
                    redisService.setCacheObject(doudian_order_coupons_redis_prefix + orderIdStr, urlCode, 100L, TimeUnit.DAYS);
                    //存入店铺id
                    redisService.setCacheObject(doudian_order_coupons_redis_prefix_shop_id + orderIdStr, dShopId, 100L, TimeUnit.DAYS);
                }
                DdHexiaoWhitelist ddHexiaoWhitelist = ddHexiaoWhitelistService.selectDdHexiaoWhitelistByTeacherId(courseOrder.getTeacherId());
                if (ddHexiaoWhitelist != null && ddHexiaoWhitelist.getOpenStatus() == 1 && courseOrder.getDdShopId() != null && courseOrder.getDdShopId() > 0L) {
                    if (doudianShopConfig.getIsSupportThirdCoupon() == 1 && doudianShopConfig.getOpenThirdCoupon() == 1) {
                        doudianPriveteOrderService.whiteListHeXiao(courseOrder.getOrderId(), courseOrder.getDdShopId(), doudianShopConfig.getAppKey());
                    }
                }
            }
            if (skuOrderListItem.getFirstCid() != null && skuOrderListItem.getFirstCid() == 20015L) {
                int rows = doudianPriveteOrderService.createChuBanWuOrder(courseOrder, skuOrderListItem);
                if (rows > 0) {
                    System.out.println("创建出版物订单扣点记录成功");
                } else {
                    System.out.println("创建出版物订单扣点记录失败");
                }
            }
        }
        return AjaxResult.success();
    }

    private void processProductChange(String appId, String eventSign, String requestBody) {
        ProductChangeRequest productChangeRequest = new ProductChangeRequest();
        MsgParam param = productChangeRequest.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);

        List<DoudianOpMsgParamRecord<ProductChangeParam>> list = productChangeRequest.getRequestBody();
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for(DoudianOpMsgParamRecord<ProductChangeParam> item:list){
            ProductChangeParam data = item.getData();
            Long productId = data.getProductId();
            //先查出商品
            DoudianCourse doudianCourse = doudianCourseService.selectDoudianCourseByProductId(productId);
            if(doudianCourse==null){
                continue;
            }
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(data.getShopId());
            //商品审核通过
            if(data.getEvent()==4){
                doudianCourse.setCheckStatus(3L);
                doudianCourse.setStatus(0L);
                doudianCourseService.updateDoudianCourse(doudianCourse);

                List<SpecPricesItem> specPricesItems = fetchSpecPrices(data.getShopId(), data.getProductId(), doudianCourse);


                //同时上架渠道商品
                doudianPriveteOrderService.upChannelProduct(doudianCourse,specPricesItems,doudianShopConfig);
                if (doudianCourse.getChannelId() != null && doudianCourse.getChannelId() > 0L) {
                    AccessToken accessToken = doudianPriveteOrderService.getAccessToken(doudianCourse.getShopId());
                    doudianPriveteOrderService.onlineChannelProductAsync(doudianCourse, doudianShopConfig, accessToken);
                }


                //商品变更事件： 1: 商品创建； 2: 商品保存； 3:商品提交审核； 4：商品审核通过； 5：商品审核不通过； 6：商品被删除； 7：商品从回收站恢复； 8：商品封禁； 9：解除商品封禁； 10：下架商品； 11：上架商品； 12：商品售空； 13：终止审核商品； 14：审核通过待上架；17：彻底删除商品
                //添加商品
            }
            //商品审核不通过
            if(data.getEvent()==5){
                doudianCourse.setCheckStatus(4L);
                if(StringUtils.isNotBlank(data.getReason())){
                    doudianCourse.setCheckFailReason(data.getReason());
                }
                doudianCourseService.updateDoudianCourse(doudianCourse);
            }
            //商品封禁
            if(data.getEvent()==5){
                doudianCourse.setStatus(1L);
                doudianCourse.setCheckStatus(5L);
                if(StringUtils.isNotBlank(data.getReason())){
                    doudianCourse.setCheckFailReason(data.getReason());
                }
                doudianCourseService.updateDoudianCourse(doudianCourse);
            }
            //下架商品
            if(data.getEvent()==10){
                doudianCourse.setStatus(1L);
                doudianCourseService.updateDoudianCourse(doudianCourse);
            }
            //上架商品
            if(data.getEvent()==11){
                doudianCourse.setStatus(0L);
                doudianCourse.setCheckStatus(3L);
                doudianCourseService.updateDoudianCourse(doudianCourse);
                List<SpecPricesItem> specPricesItems = fetchSpecPrices(data.getShopId(), data.getProductId(), doudianCourse);
                //同时上架渠道商品
                doudianPriveteOrderService.upChannelProduct(doudianCourse,specPricesItems,doudianShopConfig);
                if (doudianCourse.getChannelId() != null && doudianCourse.getChannelId() > 0L) {
                    AccessToken accessToken = doudianPriveteOrderService.getAccessToken(doudianCourse.getShopId());
                    doudianPriveteOrderService.onlineChannelProductAsync(doudianCourse, doudianShopConfig, accessToken);
                }
            }
            //审核通过待上架
            if(data.getEvent()==14){
                doudianCourse.setCheckStatus(3L);
                doudianCourseService.updateDoudianCourse(doudianCourse);
            }
        }
    }

    private List<SpecPricesItem> fetchSpecPrices(Long shopId, Long productId, DoudianCourse doudianCourse) {
        DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(shopId);
        if (doudianShopConfig == null) {
            return Collections.emptyList();
        }
        AccessToken accessToken = doudianPriveteOrderService.getAccessToken(shopId);
        ProductDetailRequest request = new ProductDetailRequest();
        request.setAppKey(doudianShopConfig.getAppKey());
        ProductDetailParam param11 = request.getParam();
        param11.setProductId(String.valueOf(productId));
        ProductDetailResponse response = request.execute(accessToken);
        if ("10000".equals(response.getCode())) {
            ProductDetailData productDetailData = response.getData();
            List<SpecPricesItem> specPrices = productDetailData.getSpecPrices();
            DoudianCourse updateDoudianCourse = new DoudianCourse();
            updateDoudianCourse.setId(doudianCourse.getId());
            updateDoudianCourse.setSpecPrices(JSON.toJSONString(specPrices));
            updateDoudianCourse.setSpecPricesV2(JSON.toJSONString(specPrices));
            doudianCourseService.updateDoudianCourse(updateDoudianCourse);
            return productDetailData.getSpecPrices();
        }
        return Collections.emptyList();
    }

    @GetMapping("/generate_xhs_wx_link_if_not_present_2024_08_13")
    public AjaxResult generateXhsWxLink(@RequestParam(value ="orderPrimaryId") Long orderPrimaryId) {
        CourseOrder courseOrder = courseOrderService.selectCourseOrderById(orderPrimaryId);
        if (courseOrder!=null&& courseOrder.getOrderStatus()!=null&&(courseOrder.getOrderStatus() == 1||courseOrder.getOrderStatus()==6)) {
            String wxAppId = null;
            if(courseOrder.getAppNameType()==1){
                wxAppId="wx3b0ef5cd084c2e38";
            }else{
                wxAppId="wx0e5e01d239197bb1";
            }

            //发送手机短信
            if (StringUtils.isNotBlank(courseOrder.getBuyerUserMobile())) {
                String s = "xhs_"+courseOrder.getOrderId()+"_"+courseOrder.getBuyerUserMobile();
                GenerateUrlLinkRequestExt dto = new GenerateUrlLinkRequestExt();
                dto.setAppId(wxAppId);
                dto.setPath("/pages_details/details/details");
                dto.setQuery("course_id=" + courseOrder.getCourseId()+"&platform=1&appNameType="+courseOrder.getAppNameType()+"&tempSeeSecret="+s);
                dto.setEnvVersion("release");
                dto.setExpireInterval(30);
                dto.setExpireType(1);
                dto.setIsExpire(true);
                String key = "wxcodexhs:" +courseOrder.getBuyerUserId()+ courseOrder.getId();
                String link1 = redisService.getCacheObject(key);
                if (StringUtils.isBlank(link1)) {
                    AjaxResult ajaxResult = wxMaLinkService.generateUrlLink(dto);
                    if (ajaxResult.isSuccess()) {
                        String linkUrl = (String) ajaxResult.get("data");
                        redisService.setCacheObject(key, linkUrl, 29L, TimeUnit.DAYS);
                    }
                    return ajaxResult;
                }else{
                    return AjaxResult.success("链接获取成功!",link1);
                }
            }
        }
        return AjaxResult.error();
    }


    /**
     * 小红书支付回调
     * @param request
     * @return
     */
    @PostMapping("/callback")
    public AjaxResult xhsCallback(HttpServletRequest request) {
        Map<Integer,String> payWayMap = new HashMap<>();
        //1：支付宝 2：微信 3：apple 内购 4：apple pay 5：花呗分期 7：支付宝免密支付 8：云闪付 -1：其他
        payWayMap.put(1,"支付宝支付");
        payWayMap.put(2,"微信支付");
        payWayMap.put(3,"apple 内购");
        payWayMap.put(4,"apple pay");
        payWayMap.put(5,"花呗分期");
        payWayMap.put(7,"支付宝支付");
        payWayMap.put(8,"银行卡支付");
        payWayMap.put(-1,"其他");
        String requestBody = null;
        try {
            requestBody = TradeSystemSign.getRequestBody(request);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        System.out.println("接收到的信息:" + requestBody);
        //[{"data":"{\"buyable\":false,\"itemId\":\"665ecffd5ea9b500016238ed\",\"updateTime\":1717489661000,\"skuId\":\"665ecffd5ea9b500016238ed\"}","msgTag":"msg_sku_buyable","sellerId":"6645c0efc7b2730001d972ef"}]
        List<JSONObject> jsonObjects = JSON.parseArray(requestBody, JSONObject.class);
        for (JSONObject json0 : jsonObjects) {
            JSONObject json = json0.getJSONObject("data");
            System.out.println("json0:" + JSON.toJSONString(json0));
            System.out.println("json:" + JSON.toJSONString(json));
            if (json0.getString("msgTag").equals("msg_item_audit_reject")) {
                //商品审核驳回
                String itemId = json.getString("itemId");
                long updateTime = json.getLong("updateTime");
                //更新商品为审核失败
                XhsCourse xhsCourse = xhsCourseService.selectXhsCourseByItemId(itemId);
                if (xhsCourse != null) {
                    xhsCourse.setAuditStatus(2);
                    xhsCourse.setAuditFailReason("商品审核失败!");
                    xhsCourseService.updateXhsCourse(xhsCourse);
                }
            }
            if (json0.getString("msgTag").equals("msg_sku_buyable")) {
                String skuId = json.getString("skuId");
                XhsCourse xhsCourse = xhsCourseService.selectXhsCourseBySkuId(skuId);
                if (xhsCourse != null) {
                    //查询商品状态
                    String xhsId = xhsCourse.getXhsId();
                    GetItemInfoRequest getItemInfoRequest = new GetItemInfoRequest();
                    getItemInfoRequest.setItemId(xhsId);
                    getItemInfoRequest.setPageSize(1);
                    getItemInfoRequest.setPageNo(1);
                    GetItemInfoResponse itemInfo = xhsGoodsService.getItemInfo(getItemInfoRequest);
                    if (itemInfo != null) {
                        List<SkuDetail> skuInfos = itemInfo.getSkuInfos();
                        if(CollectionUtils.isNotEmpty(skuInfos)){
                            SkuDetail skuDetail = skuInfos.get(0);
                            if (skuDetail != null) {
                                boolean isBuy = skuDetail.isBuyable();
                                if (isBuy) {
                                    //可以购买,商品审核状态必然也是成功的
                                    xhsCourse.setAvailable(1);
                                    xhsCourse.setAuditStatus(1);
                                } else {
                                    xhsCourse.setAvailable(0);
                                }
                                xhsCourseService.updateXhsCourse(xhsCourse);
                            }
                        }
                    }
                }

            }
            if (json0.getString("msgTag").equals("msg_fulfillment_status_change")) {
                //重复消息处理，如果已经存在则不处理
                String orderId = json.getString("orderId");
                Integer orderStatus = json.getInteger("orderStatus");
                String keyPrefix = "msg_fulfillment_status_change:";
                String keySuffix = orderId+"_"+orderStatus;
                if (redisService.hasKey(keyPrefix+keySuffix)){
                    return AjaxResult.success();
                }
                redisService.setCacheObject(keyPrefix+keySuffix,keySuffix,60L,TimeUnit.DAYS);
                GetOrderDetailResponse orderDetail = xhsGoodsService.getOrderDetail(orderId);
                if (orderDetail == null) {
                    System.out.println("查询订单失败了!");
                    return AjaxResult.success();
                }
                //支付方式 1：支付宝 2：微信 3：apple 内购 4：apple pay 5：花呗分期 7：支付宝免密支付 8：云闪付 -1：其他
                //支付方式
                Integer paymentType = orderDetail.getPaymentType();
                List<GetOrderDetailResponse.OrderSkuDTOV3> skuList = orderDetail.getSkuList();
                XhsOrderSkuDTO xhsOrderSkuDTO = new XhsOrderSkuDTO();
                xhsOrderSkuDTO.setXhsUserId(orderDetail.getUserId());
                xhsOrderSkuDTO.setOrderId(orderId);
                //查询用户地址数据
                GetOrderReceiverInfoRequest getOrderReceiverInfo = new GetOrderReceiverInfoRequest();
                GetOrderReceiverInfoRequest.OrderReceiverQuery orderReceiverQuery = new GetOrderReceiverInfoRequest.OrderReceiverQuery();
                orderReceiverQuery.setOrderId(orderId);
                orderReceiverQuery.setOpenAddressId(orderDetail.getOpenAddressId());
                getOrderReceiverInfo.setIsReturn(false);
                getOrderReceiverInfo.setReceiverQueries(Collections.singletonList(orderReceiverQuery));
                OrderReceiverInfo orderReceiverInfo = xhsGoodsService.getOrderReceiverInfo(getOrderReceiverInfo);
                if (orderReceiverInfo != null) {
                    BatchDecryptRequest request11 = new BatchDecryptRequest();
                    BatchDecryptRequest.baseInfo baseInfo = new BatchDecryptRequest.baseInfo();
                    baseInfo.setDataTag(orderId);
                    baseInfo.setEncryptedData(orderReceiverInfo.getReceiverName());
                    BatchDecryptRequest.baseInfo baseInfo1 = new BatchDecryptRequest.baseInfo();
                    baseInfo1.setDataTag(orderId);
                    baseInfo1.setEncryptedData(orderReceiverInfo.getReceiverPhone());
                    List<BatchDecryptRequest.baseInfo> list = new ArrayList<>();
                    list.add(baseInfo);
                    list.add(baseInfo1);
                    request11.setActionType("1");
                    request11.setAppUserId("665072d4e300000000000001");
                    request11.setBaseInfos(list);
                    List<DecryptedInfo> batchDecryptInfo = xhsGoodsService.getBatchDecryptInfo(request11);
                    if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(batchDecryptInfo) && batchDecryptInfo.size() == 2) {
                        xhsOrderSkuDTO.setReceiverName(batchDecryptInfo.get(0).getDecryptedData());
                        xhsOrderSkuDTO.setReceiverPhone(batchDecryptInfo.get(1).getDecryptedData());
                    }
                }
                xhsOrderSkuDTO.setTotalPayAmount(orderDetail.getTotalPayAmount());
                if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(skuList)) {
                    List<SkuOrderInfo> skuInfoList = new ArrayList<>();
                    for (GetOrderDetailResponse.OrderSkuDTOV3 sku : skuList) {
                        String skuId = sku.getSkuId();
                        XhsCourse xhsCourse = xhsCourseService.selectXhsCourseBySkuId(skuId);
                        if (xhsCourse == null) {
                            System.out.println("skuId:" + skuId + "找不到对应的课程!");
                            return AjaxResult.success();
                        }
                        SkuOrderInfo skuOrderInfo = new SkuOrderInfo();
                        skuOrderInfo.setCourseId(xhsCourse.getCourseId());
                        skuOrderInfo.setSkuId(skuId);
                        skuOrderInfo.setTotalPaidAmount(sku.getTotalPaidAmount());
                        skuInfoList.add(skuOrderInfo);
                        if (StringUtils.isNotEmpty(sku.getKolId())) {
                            System.out.println("找到了小红书KolId:" + sku.getKolId());
                        }
                        if (StringUtils.isNotEmpty(sku.getKolName())) {
                            System.out.println("找到了小红书Kol名字:" + sku.getKolName());
                        }
                    }
                    xhsOrderSkuDTO.setSkuInfoList(skuInfoList);
                }
                if (orderStatus == 1) {
                    //已下单待付款
                    if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(xhsOrderSkuDTO.getSkuInfoList())) {
                        System.out.println("发送创建订单请求!!参数为:" + JSON.toJSONString(xhsOrderSkuDTO));
                        this.createXhsOrder(xhsOrderSkuDTO);
                    }
                }
                if (orderStatus == 2) {
                    //已支付处理中
                    if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(xhsOrderSkuDTO.getSkuInfoList())) {
                        System.out.println("发送支付订单请求!!参数为:" + JSON.toJSONString(xhsOrderSkuDTO));
                        String payWay = payWayMap.get(paymentType);
                        xhsOrderSkuDTO.setPayWay(payWay);
                        this.changeXhsOrderToPaid(xhsOrderSkuDTO);
                    }
                }
                if (orderStatus == 4) {
                    //待发货
                    if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(xhsOrderSkuDTO.getSkuInfoList())) {
                        System.out.println("发送发货请求!!参数为:" + JSON.toJSONString(xhsOrderSkuDTO));
                        this.xhsDelivery(xhsOrderSkuDTO);
                        //设置为已发货
                        OrderDeliverRequest orderDeliverRequest = new OrderDeliverRequest();
                        orderDeliverRequest.setOrderId(orderId);
                        orderDeliverRequest.setExpressNo("线上课程");
                        orderDeliverRequest.setExpressCompanyCode("selfdelivery");
                        boolean b = xhsGoodsService.orderDeliver(orderDeliverRequest);
                        if (b) {
                            System.out.println("发货成功!!");
                        } else {
                            System.out.println("发货失败!!");
                        }
                    }
                }
            }
            if (json0.getString("msgTag").equals("msg_after_sale_create")) {
                System.out.println("用户发起售后!");
                String refundId = json.getString("returnsId");
                String outOrderNumber = json.getString("orderId");
                Integer returnType = json.getInteger("returnType");
                BigDecimal returnMoney = json.getBigDecimal("refundFee");
                //防止重入：
                String keyPrefix = "msg_after_sale_create:";
                if (redisService.hasKey(keyPrefix+refundId)){
                    return AjaxResult.success();
                }
                redisService.setCacheObject(keyPrefix+refundId,refundId,60L,TimeUnit.DAYS);
                //创建退款单
                CourseOrder courseOrder = courseOrderService.selectCourseOrderByOutOrderNumber(outOrderNumber);
                Long buyerUserId = courseOrder.getBuyerUserId();
                WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(buyerUserId);
                String openId = wendaoUser.getOpenId();
                //检测是否重复,防止并发
                GetAfterSaleInfoRequest request11 = new GetAfterSaleInfoRequest();
                request11.setReturnsId(refundId);
                GetAfterSaleInfoResponse.OpenAPIAfterSaleInfo afterSaleInfo = xhsGoodsService.getAfterSaleInfo(request11);
                String reason = afterSaleInfo.getReasonNameZh();
                CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(refundId);
                CreateWendaoRefundDTO createWendaoRefundDTO = new CreateWendaoRefundDTO();
                createWendaoRefundDTO.setOpenid(openId);
                createWendaoRefundDTO.setRefundReason(reason);
                createWendaoRefundDTO.setOrderId(courseOrder.getOrderId());

                List<String> proofPhotos = afterSaleInfo.getProofPhotos();
                if (CollectionUtils.isNotEmpty(proofPhotos)) {
                    createWendaoRefundDTO.setReceiptImg(StringUtils.join(proofPhotos, ","));
                }
                createWendaoRefund(createWendaoRefundDTO, refundId, courseRefund, returnMoney,7);
                /**
                 * 参数名称	参数类型	参数描述
                 * returnsId	string	售后id
                 * orderId	string	订单Id
                 * returnType	int	退货类型 1 退货退款 2 换货 3 仅退款(old) 4仅退款(已发货) 5未发货仅退款(未发货取消订单)
                 * requestFrom	int	售后发起主体：1 买家申请 2 卖家申请 3 平台客服发起 4 系统修改
                 * refundFee	number	退款金额（不包含运费）（单位：元）
                 * updateTime	long	更新时间（毫秒）
                 */

            }
            if (json0.getString("msgTag").equals("msg_after_sale_refund_finished")) {
                //退款成功消息
                //售后Id
                String refundId = json.getString("returnsId");
//                String outOrderNumber = json.getString("orderId");
//                Integer returnType = json.getInteger("returnType");
//                BigDecimal returnMoney = json.getBigDecimal("refundFee");
                String keyPrefix = "msg_after_sale_refund_finished:";
                if (redisService.hasKey(keyPrefix+refundId)){
                    return AjaxResult.success();
                }
                redisService.setCacheObject(keyPrefix+refundId,refundId,60L,TimeUnit.DAYS);
                System.out.println("退款成功");
                CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(refundId);
                String orderId = courseRefund.getOrderId();
                courseRefund.setRefundStatus(1);
                courseRefund.setRefundType(1);
                CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
                if (courseOrder != null) {
                    System.out.println("订单不为空,设置订单为已退款");
                    courseOrder.setOrderStatus(2);
                    courseOrderService.updateCourseOrder(courseOrder);
                }
                courseRefundService.updateCourseRefund(courseRefund);
                //退款成功 修改资金状态 备注信息
                wendaoSettlementService.modifyFundsInfo(courseRefund, courseOrder);
            }
            //售后取消
            if (json0.getString("msgTag").equals("msg_after_sale_closed")) {
                //售后关闭
                String refundId = json.getString("returnsId");
                String outOrderNumber = json.getString("orderId");
                Integer returnType = json.getInteger("returnType");
                Integer requestFrom = json.getInteger("requestFrom");
                BigDecimal refundFee = json.getBigDecimal("refundFee");
                Long updateTime = json.getLong("updateTime");

                String keyPrefix = "msg_after_sale_closed:";
                if (redisService.hasKey(keyPrefix+refundId)){
                    return AjaxResult.success();
                }
                redisService.setCacheObject(keyPrefix+refundId,refundId,60L,TimeUnit.DAYS);

                //查询小红书的售后详情
                //检测是否重复,防止并发
                GetAfterSaleInfoRequest afterSaleRequest =  new GetAfterSaleInfoRequest();
                afterSaleRequest.setReturnsId(refundId);
                GetAfterSaleInfoResponse.OpenAPIAfterSaleInfo afterSaleInfo = xhsGoodsService.getAfterSaleInfo(afterSaleRequest);
                if(afterSaleInfo!=null){
                    //处理售后关闭,5已取消
                    if(afterSaleInfo.getStatus()==5){
                        //修改订单状态
                        //删除退款单
                        CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(refundId);
                        if(courseRefund!=null){
                            String orderId = courseRefund.getOrderId();
                            courseRefundService.deleteCourseRefundById(courseRefund.getId());
                            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
                            if(courseOrder!=null){
                                courseOrder.setOrderStatus(1);
                                courseOrderService.updateCourseOrder(courseOrder);
                                //售后取消通知
                                MessageNotification messageNotification = new MessageNotification();
                                messageNotification.setId(courseOrder.getBuyerUserId());
                                messageNotification.setTeacherId(courseOrder.getTeacherId());
                                messageNotification.setUserId(null);
                                messageNotification.setNoticeTitle("「 小红书售后取消通知 」 用户购买的课程《" + courseOrder.getCourseTitle() + "》取消了售后");
                                messageNotification.setNoticeContent("用户：" + courseOrder.getBuyerUserName() + "\\n" + "取消了售后,订单号:"+ courseOrder.getOrderId() + "\\n"+ "课程名称：" + courseOrder.getCourseTitle());
                                messageNotification.setNoticeType(7);
                                messageNotification.setIsDelete(0);
                                messageNotification.setReadStatus(0);
                                messageNotification.setCreateTime(DateUtils.getNowDate());
                                messageNotification.setUpdateTime(DateUtils.getNowDate());
                                messageNotificationService.insertMessageNotification(messageNotification);
                            }else{
                                System.out.println("订单不存在");
                            }
                        }else{
                            System.out.println("售后单不存在");
                        }
                    }
                }else{
                    System.out.println("系统错误!");
                }
            }
        }
        return AjaxResult.success();
    }

    private void createWendaoRefund(CreateWendaoRefundDTO createWendaoRefundDTO, String refundId, CourseRefund courseRefundOld, BigDecimal returnMoney, Integer platform) {
        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(createWendaoRefundDTO.getOrderId());
        if (courseOrder == null) {
            return;
        }
        WendaoUser wendaoUser = wendaoUserService.selectWendaoUserByOpenid(createWendaoRefundDTO.getOpenid(), courseOrder.getAppNameType());
        Long wendaoUserId = wendaoUser.getId();
        if (!Objects.equals(wendaoUserId, courseOrder.getBuyerUserId())) {
            return;
        }
        //创建本侧退款单
        CourseRefund courseRefund = courseRefundOld == null ? new CourseRefund() : courseRefundOld;
        courseRefund.setBuyerUserId(wendaoUserId);
        courseRefund.setOrderId(courseOrder.getOrderId());
        courseRefund.setRefundPrice(returnMoney);
        //reason
        courseRefund.setRefundReason(createWendaoRefundDTO.getRefundReason());
        courseRefund.setSupplimentalDescription(createWendaoRefundDTO.getSupplimentalDescription());
        courseRefund.setReceiptImg(createWendaoRefundDTO.getReceiptImg());
        courseRefund.setIsDelete(0);
        //需要退款审核
        courseRefund.setRefundStatus(0);
        courseRefund.setRefundType(0);
        courseRefund.setRefundPlatform(platform);
        //设置来源app,问到课堂或问到好课
        courseRefund.setAppNameType(courseOrder.getAppNameType());
        //设置来源
        courseRefund.setRefundId(refundId);
        courseRefund.setOutRefundId(refundId);
        Long courseId = courseOrder.getCourseId();
        String courseTitle = courseOrder.getCourseTitle();
        courseRefund.setCourseId(courseId);
        courseRefund.setCourseTitle(courseTitle);
        courseRefund.setCoursePrice(courseOrder.getCoursePrice());
        courseRefund.setOriginalPrice(courseOrder.getOriginalPrice());
        courseRefund.setCourseImgUrl(courseOrder.getCourseImgUrl());
        courseRefund.setBuyerUserName(wendaoUser.getUserName());
        courseRefund.setBuyerUserMobile(wendaoUser.getTelNumber());
        courseRefund.setRefundTime(new Date());
        courseRefund.setTeacherId(courseOrder.getTeacherId());
        courseRefund.setOrderTime(courseOrder.getPayTime());
        courseRefund.setUpdateTime(new Date());

        courseRefund.setCourseDuration(courseOrder.getCourseDuration());
        courseRefund.setStudyDuration(courseOrder.getStudyDuration());

        if (courseRefund.getId() != null) {
            //状态不修改,有可能是消息后到
            courseRefund.setRefundStatus(null);
            courseRefund.setRefundType(null);
            courseRefundService.updateCourseRefund(courseRefund);
        } else {
            courseRefundService.insertCourseRefund(courseRefund);
        }
        //courseOrder.setOrderStatus(4);
        courseOrderService.updateCourseOrderNotTwo(createWendaoRefundDTO.getOrderId(),4);

        //退款通知
        MessageNotification messageNotification = new MessageNotification();
        messageNotification.setId(courseOrder.getBuyerUserId());
        messageNotification.setTeacherId(courseOrder.getTeacherId());
        messageNotification.setUserId(null);
        messageNotification.setNoticeTitle("「 退款通知 」 用户购买的课程《" + courseOrder.getCourseTitle() + "》发起了退款");
        String userName = "用户" + courseOrder.getBuyerUserId();
        userName = com.wendao101.common.core.utils.StringUtils.isBlank(courseOrder.getBuyerUserName()) ? userName : courseOrder.getBuyerUserName();
        messageNotification.setNoticeContent("用户昵称：" + userName + "\\n" + "退款金额：" + courseOrder.getPayPrice() + "\\n" + "课程名称：" + courseOrder.getCourseTitle());
        messageNotification.setNoticeType(7);
        messageNotification.setIsDelete(0);
        messageNotification.setReadStatus(0);
        messageNotification.setCreateTime(DateUtils.getNowDate());
        messageNotification.setUpdateTime(DateUtils.getNowDate());
        messageNotificationService.insertMessageNotification(messageNotification);

        //执行自动退款
        if (courseRefund.getRefundPlatform() == 11) {
            if (courseOrder.getDdHexiaoStatus() != null && courseOrder.getDdHexiaoStatus() == 1) {
                return;
            }
            //如果是虚拟商品
            String refundReason = courseRefund.getRefundReason();
            if (StringUtils.isNotBlank(refundReason)) {
                if (refundReason.contains("不符")) {
                    redisService.setCacheObject(REFUND_REASON_KEY_PREFIX + courseRefund.getOrderId(), refundReason, 30L, TimeUnit.DAYS);
                    return;
                }
                if (refundReason.contains("无法")) {
                    redisService.setCacheObject(REFUND_REASON_KEY_PREFIX + courseRefund.getOrderId(), refundReason, 30L, TimeUnit.DAYS);
                    return;
                }
                if (AfterSaleReasonsNeedHandle.fetchReasonList().contains(refundReason)) {
                    redisService.setCacheObject(REFUND_REASON_KEY_PREFIX + courseRefund.getOrderId(), refundReason, 30L, TimeUnit.DAYS);
                    return;
                }
                String refundReasonRedis = redisService.getCacheObject(REFUND_REASON_KEY_PREFIX + courseRefund.getOrderId());
                if (StringUtils.isBlank(refundReasonRedis)) {
                    //处理退款
                    courseRefundService.autoRefundProcess(courseRefund);
                }
            }
        }
        //发送微信申请退款消息
        RefundApplyMessage refundMessage = new RefundApplyMessage();
        refundMessage.setTeacherId(courseOrder.getTeacherId());
        refundMessage.setOrderId(courseOrder.getOrderId());
        refundMessage.setCourseTitle(courseOrder.getCourseTitle());
        refundMessage.setRefundReason(courseRefund.getRefundReason());
        refundMessage.setRefundPrice(courseRefund.getRefundPrice());
        refundMessage.setRefundTime(courseRefund.getRefundTime());
        wxSendMessageService.sendRefundApplyMessage(refundMessage);
    }

    /**
     * h5直播购买
     * @param createWendaoLiveOrderDTO
     * @return
     */
    @PostMapping("/createWendaoLiveWapOrder")
    public AjaxResult createWendaoLiveWapOrder(@RequestBody CreateWendaoLiveOrderDTO createWendaoLiveOrderDTO) {
        if (createWendaoLiveOrderDTO.getLiveId() == null) {
            return AjaxResult.error("直播id不能为空");
        }
        WendaoLiveDTO wendaoLiveDTO = courseAuditService.selectWendaoLiveWapById(createWendaoLiveOrderDTO.getLiveId());
        if (wendaoLiveDTO == null) {
            return AjaxResult.error("直播不存在");
        }
        //获取老师
        TeacherDTO teacherDTO = courseOrderService.selectTeacherByTeacherId(wendaoLiveDTO.getTeacherId());
        if(teacherDTO==null){
            return AjaxResult.error("老师不存在");
        }
        //获取价格
        BigDecimal price = wendaoLiveDTO.getPrice();
        //创建订单
        WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(createWendaoLiveOrderDTO.getUserId());
        if (wendaoUser == null) {
            return AjaxResult.error("用户信息不存在,请检查userId");
        }
        Long wendaoUserId = wendaoUser.getId();
        String yyyyMMdd = DateUtils.dateTime();
        String orderId = yyyyMMdd + wendaoUserId + String.format("%04d", redisService.incr(yyyyMMdd, wendaoUserId));
        CourseOrder courseOrder = new CourseOrder();
        courseOrder.setOrderId(orderId);
        //直播时长
        long millSecond = wendaoLiveDTO.getEndTime().getTime() - wendaoLiveDTO.getStartTime().getTime();
        //是否赠送课程
        courseOrder.setCourseDuration(millSecond / 1000L);
        courseOrder.setCourseId(wendaoLiveDTO.getId());
        courseOrder.setTeacherId(wendaoLiveDTO.getTeacherId());
        courseOrder.setCourseImgUrl(wendaoLiveDTO.getCoverUrl());
        courseOrder.setValidity(999999L);
        courseOrder.setCourseTitle(wendaoLiveDTO.getLiveName());
        courseOrder.setCoursePrice(wendaoLiveDTO.getPrice());
        //使用老师的appNameType
        courseOrder.setAppNameType(teacherDTO.getAppNameType());
        courseOrder.setPayPrice(price);
        courseOrder.setIsCourse(0);
        courseOrder.setOriginalPrice(wendaoLiveDTO.getOriginalPrice());
        courseOrder.setOrderStatus(0);
        //以这个来判断是否是直播订单!!!
        courseOrder.setOrderType(2);
        courseOrder.setOrderTime(new Date());
        courseOrder.setFundsType(0);
        courseOrder.setOrderPlatform(5);
        courseOrder.setBuyerUserId(wendaoUser.getId());
        courseOrder.setBuyerUserImg(wendaoUser.getAvatarUrl());
        courseOrder.setBuyerUserName(wendaoUser.getNickName());
        courseOrder.setBuyerUserMobile(wendaoUser.getTelNumber());
        courseOrder.setMyEarningsPrice(price);
        courseOrderService.insertCourseOrder(courseOrder);
        Map<String, String> map = new HashMap<>();
        map.put("orderId", orderId);
        return AjaxResult.success(map);
    }

    /**
     * 直播购买
     * @param createWendaoLiveOrderDTO
     * @return
     */
    @PostMapping("/createWendaoLiveOrder")
    public AjaxResult createWendaoLiveOrder(@RequestBody CreateWendaoLiveOrderDTO createWendaoLiveOrderDTO) {
        if (createWendaoLiveOrderDTO.getLiveId() == null) {
            return AjaxResult.error("直播id不能为空");
        }
        WendaoLiveDTO wendaoLiveDTO = courseAuditService.selectWendaoLiveById(createWendaoLiveOrderDTO.getLiveId());
        if (wendaoLiveDTO == null) {
            return AjaxResult.error("直播不存在不存在");
        }
        //获取价格
        BigDecimal price = wendaoLiveDTO.getPrice();
        //创建订单
        WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(createWendaoLiveOrderDTO.getUserId());
        if (wendaoUser == null) {
            return AjaxResult.error("用户信息不存在,请检查userId");
        }
        Long wendaoUserId = wendaoUser.getId();
        String yyyyMMdd = DateUtils.dateTime();
        String orderId = yyyyMMdd + wendaoUserId + String.format("%04d", redisService.incr(yyyyMMdd, wendaoUserId));
        CourseOrder courseOrder = new CourseOrder();
        courseOrder.setOrderId(orderId);
        //直播时长
        long millSecond = wendaoLiveDTO.getEndTime().getTime() - wendaoLiveDTO.getStartTime().getTime();
        //是否赠送课程
        courseOrder.setCourseDuration(millSecond / 1000L);
        courseOrder.setCourseId(wendaoLiveDTO.getId());
        courseOrder.setTeacherId(wendaoLiveDTO.getTeacherId());
        courseOrder.setCourseImgUrl(wendaoLiveDTO.getCoverUrl());
        courseOrder.setValidity(999999L);
        courseOrder.setCourseTitle(wendaoLiveDTO.getLiveName());
        courseOrder.setCoursePrice(wendaoLiveDTO.getPrice());
        courseOrder.setAppNameType(wendaoUser.getAppNameType());
        courseOrder.setPayPrice(price);
        courseOrder.setIsCourse(0);
        courseOrder.setOriginalPrice(wendaoLiveDTO.getOriginalPrice());
        courseOrder.setOrderStatus(0);
        //以这个来判断是否是直播订单!!!
        courseOrder.setOrderType(2);
        courseOrder.setOrderTime(new Date());
        courseOrder.setFundsType(0);
        courseOrder.setOrderPlatform(8);
        courseOrder.setBuyerUserId(wendaoUser.getId());
        courseOrder.setBuyerUserImg(wendaoUser.getAvatarUrl());
        courseOrder.setBuyerUserName(wendaoUser.getNickName());
        courseOrder.setBuyerUserMobile(wendaoUser.getTelNumber());
        courseOrder.setMyEarningsPrice(price);
        courseOrderService.insertCourseOrder(courseOrder);
        Map<String, String> map = new HashMap<>();
        map.put("orderId", orderId);
        return AjaxResult.success(map);
    }


    @PostMapping("/createXhsOrder")
    public AjaxResult createXhsOrder(@RequestBody XhsOrderSkuDTO xhsOrderSkuDTO) {
        //调用创建订单
        System.out.println("创建小红书订单,入参:"+   JSON.toJSONString(xhsOrderSkuDTO));
        if (StringUtils.isBlank(xhsOrderSkuDTO.getOrderId()) || CollectionUtils.isEmpty(xhsOrderSkuDTO.getSkuInfoList())) {
            System.out.println("参数错误");
            return AjaxResult.error("参数错误");
        }
        for (SkuOrderInfo skuOrderInfo : xhsOrderSkuDTO.getSkuInfoList()) {
            XhsCourse xhsCourse = xhsCourseService.selectXhsCourseByCourseId(skuOrderInfo.getCourseId() == null ? -1L : skuOrderInfo.getCourseId());
            //创建多个订单
            CourseAudit courseAudit = courseAuditService.selectCourseAuditById(skuOrderInfo.getCourseId());
            if (courseAudit == null) {
                System.out.println("课程不存在");
                return AjaxResult.error("课程不存在");
            }
            Integer appNameType = courseAudit.getAppNameType();
            WendaoUser wendaoUser = wendaoUserService.selectWendaoUserByOpenIdPlatformAppNameType(xhsOrderSkuDTO.getXhsUserId(), appNameType, 7);
            if (wendaoUser==null) {
                //创建用户
                wendaoUser = new WendaoUser();
                wendaoUser.setOpenId(xhsOrderSkuDTO.getXhsUserId());
                wendaoUser.setPlatform(7);
                wendaoUser.setAppNameType(appNameType);
                wendaoUserService.insertWendaoUser(wendaoUser);
            }
            Long userId = wendaoUser.getId();
            String yyyyMMdd = DateUtils.dateTime();
            String orderId = yyyyMMdd + userId + String.format("%04d", redisService.incr(yyyyMMdd, userId));
            CourseOrder courseOrder = new CourseOrder();
            courseOrder.setOrderId(orderId);
            redisService.setCacheObject(xhs_order_mapping_prefix+xhsOrderSkuDTO.getOrderId()+skuOrderInfo.getSkuId(), orderId,180L, TimeUnit.DAYS);
            Integer courseDuration = courseAudit.getCourseDuration();
            courseOrder.setCourseDuration(courseDuration == null ? 0L : courseDuration.longValue());
            courseOrder.setCourseId(courseAudit.getId());
            courseOrder.setTeacherId(courseAudit.getTeacherId());
            courseOrder.setCourseImgUrl(courseAudit.getCoverPicUrl());
            courseOrder.setValidity(courseAudit.getExpirationDay());
            courseOrder.setCourseTitle(courseAudit.getTitle());
            courseOrder.setCoursePrice(courseAudit.getPrice());
            courseOrder.setAppNameType(wendaoUser.getAppNameType());
            BigDecimal price = BaseWxPayRequest.fen2Yuan(new BigDecimal(skuOrderInfo.getTotalPaidAmount()));
            courseOrder.setPayPrice(price);
            courseOrder.setIsCourse(0);
            courseOrder.setOriginalPrice(null);
            if(xhsCourse!=null){
                if(xhsCourse.getOriginalPrice()!=null&&xhsCourse.getOriginalPrice()>0){
                    BigDecimal oPrice = BaseWxPayRequest.fen2Yuan(new BigDecimal(xhsCourse.getOriginalPrice()));
                    courseOrder.setOriginalPrice(oPrice);
                }
            }
            courseOrder.setOrderStatus(0);
            //以这个来判断是否是直播订单!!!
            courseOrder.setOrderType(7);
            courseOrder.setOrderTime(new Date());
            courseOrder.setFundsType(0);
            courseOrder.setOrderPlatform(7);
            courseOrder.setBuyerUserId(userId);
            courseOrder.setOutOrderNumber(xhsOrderSkuDTO.getOrderId());
            courseOrder.setMyEarningsPrice(price);
            courseOrderService.insertCourseOrder(courseOrder);
        }
        return AjaxResult.success();
    }

    @PostMapping("/changeXhsOrderToPaid")
    public AjaxResult changeXhsOrderToPaid(@RequestBody XhsOrderSkuDTO xhsOrderSkuDTO) {
        if (StringUtils.isBlank(xhsOrderSkuDTO.getOrderId()) || CollectionUtils.isEmpty(xhsOrderSkuDTO.getSkuInfoList())) {
            return AjaxResult.error("参数错误");
        }
        for (SkuOrderInfo skuOrderInfo : xhsOrderSkuDTO.getSkuInfoList()) {
            //创建多个订单
            CourseAudit courseAudit = courseAuditService.selectCourseAuditById(skuOrderInfo.getCourseId());
            if (courseAudit == null) {
                return AjaxResult.error("可成不存在");
            }
            String orderId = redisService.getCacheObject(xhs_order_mapping_prefix + xhsOrderSkuDTO.getOrderId() + skuOrderInfo.getSkuId());
            if (StringUtils.isBlank(orderId)) {
                continue;
            }
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
            if (courseOrder == null) {
                //不存在!
                continue;
            }
            if (courseOrder.getOrderStatus() == 1) {
                //已支付
                continue;
            }
            Long buyerUserId = courseOrder.getBuyerUserId();
            WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(buyerUserId);
            wendaoUser.setUserName(xhsOrderSkuDTO.getReceiverName());
            wendaoUser.setTelNumber(xhsOrderSkuDTO.getReceiverPhone());
            wendaoUser.setNickName(xhsOrderSkuDTO.getReceiverName());
            wendaoUserService.updateWendaoUser(wendaoUser);
            courseOrder.setBuyerUserName(xhsOrderSkuDTO.getReceiverName());
            courseOrder.setBuyerUserMobile(xhsOrderSkuDTO.getReceiverPhone());
            courseOrder.setSettleStatus(1);
            courseOrder.setOrderStatus(1);
            courseOrder.setPayTime(new Date());
            //支付方式
            if(StringUtils.isNotBlank(xhsOrderSkuDTO.getPayWay())){
                courseOrder.setPayWay(xhsOrderSkuDTO.getPayWay());
            }else{
                courseOrder.setPayWay("支付宝支付");
            }
            courseOrder.setTradingOrderNumber(RandomStringUtils.randomNumeric(20));
            courseOrderService.updateCourseOrder(courseOrder);
            //创建流水
            wendaoSettlementService.createFundsRecord(courseOrder,null);
        }
        return AjaxResult.success();
    }

    @PostMapping("/xhsDelivery")
    public AjaxResult xhsDelivery(@RequestBody XhsOrderSkuDTO xhsOrderSkuDTO) {
        if (StringUtils.isBlank(xhsOrderSkuDTO.getOrderId()) || CollectionUtils.isEmpty(xhsOrderSkuDTO.getSkuInfoList())) {
            return AjaxResult.error("参数错误");
        }
        for (SkuOrderInfo skuOrderInfo : xhsOrderSkuDTO.getSkuInfoList()) {
            //创建多个订单
            CourseAudit courseAudit = courseAuditService.selectCourseAuditById(skuOrderInfo.getCourseId());
            if (courseAudit == null) {
                return AjaxResult.error("课程不存在");
            }
            String orderId = redisService.getCacheObject(xhs_order_mapping_prefix + xhsOrderSkuDTO.getOrderId() + skuOrderInfo.getSkuId());
            if (StringUtils.isBlank(orderId)) {
                continue;
            }
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
            if (courseOrder == null) {
                //不存在!
                continue;
            }
            //已支付发货
            if (courseOrder.getOrderStatus() == 1) {
                String wxAppId = null;
                if(courseOrder.getAppNameType()==1){
                    wxAppId="wx3b0ef5cd084c2e38";
                }else{
                    wxAppId="wx0e5e01d239197bb1";
                }

                //发送手机短信
                if (StringUtils.isNotBlank(courseOrder.getBuyerUserMobile())) {
                    String s = "xhs_"+courseOrder.getOrderId()+"_"+courseOrder.getBuyerUserMobile();
                    GenerateUrlLinkRequestExt dto = new GenerateUrlLinkRequestExt();
                    dto.setAppId(wxAppId);
                    dto.setPath("/pages_details/details/details");
                    dto.setQuery("course_id=" + courseOrder.getCourseId()+"&platform=1&appNameType="+courseOrder.getAppNameType()+"&tempSeeSecret="+s);
                    dto.setEnvVersion("release");
                    dto.setExpireInterval(30);
                    dto.setExpireType(1);
                    dto.setIsExpire(true);
                    String key = "wxcodexhs:" +courseOrder.getBuyerUserId()+ courseOrder.getId();
                    String link1 = redisService.getCacheObject(key);
                    if (StringUtils.isBlank(link1)) {
                        AjaxResult ajaxResult = wxMaLinkService.generateUrlLink(dto);
                        if (ajaxResult.isSuccess()) {
                            String linkUrl = (String) ajaxResult.get("data");
                            redisService.setCacheObject(key, linkUrl, 29L, TimeUnit.DAYS);
                            sendGuidSms(courseOrder.getBuyerUserMobile(), courseOrder.getCourseTitle(),String.valueOf(courseOrder.getBuyerUserId()), String.valueOf(courseOrder.getId()));
                        }
                    } else {
                        sendGuidSms(courseOrder.getBuyerUserMobile(), courseOrder.getCourseTitle(),String.valueOf(courseOrder.getBuyerUserId()), String.valueOf(courseOrder.getId()));
                    }
                }
            }
        }
        return AjaxResult.success();
    }

    private void sendGuidSms(String phoneNumber, String courseName, String userId,String orderId){
        try {
            StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                    .accessKeyId("LTAI5tRfCNfcpvFCRaM1ydiy")
                    .accessKeySecret("******************************")
                    .build());

            AsyncClient client = AsyncClient.builder()
                    .region("cn-hangzhou") // Region ID
                    .credentialsProvider(provider)
                    .overrideConfiguration(
                            ClientOverrideConfiguration.create()
                                    .setEndpointOverride("dysmsapi.aliyuncs.com")
                    )
                    .build();

            SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                    .signName("问到")
                    .templateCode("SMS_467545547")
                    .phoneNumbers(phoneNumber)
                    .templateParam("{\"courseName\":\""+courseName+"\",\"usercode\":\""+userId+"\",\"ordercode\":\""+orderId+"\"}")
                    .build();

            CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
            SendSmsResponse resp = response.get();
            System.out.println(new Gson().toJson(resp));
            client.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 创建问到侧订单,后执行支付预下单
     *
     * @param createWendaoOrderDTO
     * @return
     */
    @PostMapping("/createWendaoOrder")
    public AjaxResult createWendaoOrder(@RequestBody CreateWendaoOrderDTO createWendaoOrderDTO) {
        if(createWendaoOrderDTO.getPlatform() == 2 && createWendaoOrderDTO.getCourseId() == 26507L){
            return AjaxResult.error("此课程未通过快手审核,已下架,无法购买");
        }
        // 判断是哪个app
        int appNameType = createWendaoOrderDTO.getAppNameType();
        // 问到好课的app
        long courseId = createWendaoOrderDTO.getCourseId();
        String openid = createWendaoOrderDTO.getOpenid();
        long receiveCouponId = createWendaoOrderDTO.getCouponId();
        long promoterId = createWendaoOrderDTO.getPromoterId();
        CourseAudit courseAudit = courseAuditService.selectCourseAuditById(courseId);
        if (courseAudit == null) {
            return AjaxResult.error("课程信息不存在");
        }
        //获取价格
        BigDecimal price = courseAudit.getPrice();
        long secondKillId = createWendaoOrderDTO.getSecondKillId() == null ? 0 : createWendaoOrderDTO.getSecondKillId();

        ReceiveCouponInfo receiveCouponInfo = new ReceiveCouponInfo();
        //检测有效性
        SecondKill secondKillUsed = null;
        if (secondKillId > 0) {
            //秒杀
            System.out.println("秒杀逻辑");
            String secondKillUUID = createWendaoOrderDTO.getSecondKillUUID();
            String key = WendaoRedisKey.WENDAO_SECOND_KILL_FOR_FRONT_KEY + secondKillUUID;
            SecondKill secondKill = redisService.getCacheObject(key);
            if (secondKill != null) {
                //检查有效性
                secondKillUsed = secondKillService.selectValidRecordFromRedis(secondKill.getCourseId());
            }
            String keyIn = WendaoRedisKey.WENDAO_SECOND_KILL_FOR_FRONT_KEY + secondKillUUID;
            if (secondKillUsed != null) {
                redisService.setCacheObject(keyIn, secondKillUsed);
            } else {
                redisService.deleteObject(keyIn);
            }
        }
        if (secondKillUsed != null) {
            if (secondKillUsed.getSeckillPrice().compareTo(price) <= 0) {
                price = secondKillUsed.getSeckillPrice();
            }
        } else {
            //判断优惠券
            if (receiveCouponId > 0) {
                MReceiveCoupon mReceiveCoupon = mReceiveCouponService.selectMReceiveCouponById(receiveCouponId);
                if (mReceiveCoupon == null) {
                    receiveCouponInfo.setType(1);
                    mReceiveCoupon = mReceiveCouponService.selectMDiscountsCodeMapById(receiveCouponId);
                }
                long discountsId = mReceiveCoupon == null ? 0L : mReceiveCoupon.getDiscountsId();
                long newReceiveCouponId = mReceiveCoupon == null ? 0L : mReceiveCoupon.getId();
                if (discountsId > 0) {
                    MDiscounts mDiscounts = mReceiveCouponService.selectMDiscountsById(discountsId);
                    if (mDiscounts != null) {
                        BigDecimal discountsMoney = mDiscounts.getDiscountsMoney();
                        if (discountsMoney != null) {
                            price = price.subtract(discountsMoney);
                            receiveCouponInfo.setReceiveCouponId(newReceiveCouponId);
                        }
                    } else {
                        log.error("未查到优惠券,discountsId:" + discountsId);
                    }
                } else {
                    log.error("优惠券id错误!receiveCouponId:" + receiveCouponId + ",discountsId:" + discountsId);
                }
            }
        }

        //创建订单
        WendaoUser wendaoUser = null;
        CourseOrder courseOrder = new CourseOrder();
        if (StringUtils.isNotBlank(openid) && createWendaoOrderDTO.getPlatform() != 8 && createWendaoOrderDTO.getPlatform() != 9 && createWendaoOrderDTO.getPlatform() != 5) {
            wendaoUser = wendaoUserService.selectWendaoUserByOpenid(openid, appNameType);
        } else {
            wendaoUser = wendaoUserService.selectWendaoUserById(createWendaoOrderDTO.getUserId());
        }
        if(wendaoUser==null){
            return AjaxResult.error("用户信息不存在,请检查userId或openId");
        }
        int platform = createWendaoOrderDTO.getPlatform();
        if (wendaoUser.getPlatform() == 1) {
            platform = 0;
        } else if (wendaoUser.getPlatform() == 2) {
            platform = 2;
        } else if (wendaoUser.getPlatform() == 3) {
            platform = 1;
        } else if (wendaoUser.getPlatform() == 4) {
            platform = 3;
        }
        createWendaoOrderDTO.setPlatform(platform);
        Long userId = wendaoUser.getId();
        String yyyyMMdd = DateUtils.dateTime();
        String orderId = yyyyMMdd + userId + String.format("%04d", redisService.incr(yyyyMMdd, userId));

        if (receiveCouponInfo.getReceiveCouponId() > 0) {
            redisService.setCacheObject(REDIS_COUPON_KEY_PREFIX + orderId, receiveCouponInfo, 7L, TimeUnit.DAYS);
        }
        if (secondKillUsed != null) {
            redisService.setCacheObject(WendaoRedisKey.WENDAO_SECOND_KILL_KS_WX_ORDER_KEY + orderId, secondKillUsed);
        }
        courseOrder.setOrderId(orderId);
        //是否赠送课程
        List<Long> isCourse = courseOrderService.getIsCourse(courseAudit.getId());
        Integer courseDuration = courseAudit.getCourseDuration();
        courseOrder.setCourseDuration(courseDuration == null ? 0L : courseDuration.longValue());
        courseOrder.setCourseId(courseId);
        courseOrder.setTeacherId(courseAudit.getTeacherId());
        courseOrder.setCourseImgUrl(courseAudit.getCoverPicUrl());
        courseOrder.setValidity(courseAudit.getExpirationDay());
        courseOrder.setCourseTitle(courseAudit.getTitle());
        courseOrder.setCoursePrice(courseAudit.getPrice());
        courseOrder.setAppNameType(appNameType);
        courseOrder.setPayPrice(price);
        if (CollectionUtils.isNotEmpty(isCourse)) {
            courseOrder.setIsCourse(1);
        } else {
            courseOrder.setIsCourse(0);
        }
        courseOrder.setOriginalPrice(courseAudit.getOriginalPrice());
        courseOrder.setOrderStatus(0);
        courseOrder.setOrderType(0);
        courseOrder.setOrderTime(new Date());
        courseOrder.setFundsType(0);
        courseOrder.setOrderPlatform(createWendaoOrderDTO.getPlatform());
        courseOrder.setBuyerUserId(wendaoUser.getId());
        courseOrder.setBuyerUserImg(wendaoUser.getAvatarUrl());
        courseOrder.setBuyerUserName(wendaoUser.getNickName());
        courseOrder.setBuyerUserMobile(wendaoUser.getTelNumber());
        courseOrder.setMyEarningsPrice(price);
        if (promoterId > 0) {
            //推广员
            PromoterCourse promoterCourse = new PromoterCourse();
            promoterCourse.setCourseId(courseId);
            promoterCourse.setPromoterId(createWendaoOrderDTO.getPromoterId());
            List<PromoterCourse> promoterCourses = promoterCourseService.selectPromoterCourseList(promoterCourse);
            if (CollectionUtils.isNotEmpty(promoterCourses)) {
                courseOrder.setIsPromoter(1);
                courseOrder.setOrderType(1);
                PromoterCourse promoterCourse1 = promoterCourses.get(0);
                Promoter promoter = promoterService.selectPromoterById(createWendaoOrderDTO.getPromoterId());
                if (promoter != null) {
                    courseOrder.setPromoterId(createWendaoOrderDTO.getPromoterId());
                    courseOrder.setPromoterMobile(promoter.getPromoterPhone());
                    courseOrder.setPromoterName(promoter.getPromoterName());
                    long spreadRate = promoterCourse1.getSpreadRate() == null ? 0L : promoterCourse1.getSpreadRate();
                    courseOrder.setPromotionRatio(Long.toString(spreadRate));
                    if ((int) spreadRate > 0) {
                        BigDecimal multiply = price.multiply(new BigDecimal(spreadRate));
                        BigDecimal divide = multiply.divide(new BigDecimal(100));
                        divide = divide.setScale(2, RoundingMode.DOWN);
                        courseOrder.setPromoterEarningsPrice(divide);
                        courseOrder.setMyEarningsPrice(price.subtract(divide));
                    }
                }
            }
        }
        if (createWendaoOrderDTO.getPlatform() == 2) {
            if(appNameType == 2){
                String appId = StringUtils.isBlank(createWendaoOrderDTO.getAppId()) ? "ks685954519696767360" : createWendaoOrderDTO.getAppId();
                redisService.setCacheObject(KS_WENDAO_KT_OR_ZK_ORDER_APPID + courseOrder.getOrderId(), appId);
            }
            if(appNameType == 1){
                String appId = "ks699183842184529589";
                redisService.setCacheObject(KS_WENDAO_KT_OR_ZK_ORDER_APPID + courseOrder.getOrderId(), appId);
            }
        }
        courseOrderService.insertCourseOrder(courseOrder);
        Map<String, String> map = new HashMap<>();
        map.put("orderId", orderId);
        return AjaxResult.success(map);
    }

    /**
     * 微信内支付
     * @param createKuaishouPreOrderDTO
     * @return
     */
    @PostMapping("/createWxWapOrderInWeiXin")
    public AjaxResult createWxWapOrderInWeiXin(@RequestBody CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO) {
        String orderId = createKuaishouPreOrderDTO.getOrderId();
        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
        if(courseOrder==null){
            return AjaxResult.error("订单不存在");
        }
        if(createKuaishouPreOrderDTO.getUserId()==null){
            return AjaxResult.error("用户id必传");
        }
        if (courseOrder.getOrderPlatform() == 9||courseOrder.getOrderPlatform() == 5||courseOrder.getOrderPlatform() == 8) {
            WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(createKuaishouPreOrderDTO.getUserId());
            if (wendaoUser == null) {
                return AjaxResult.error("用户不存在");
            }
            if (!Objects.equals(courseOrder.getBuyerUserId(),wendaoUser.getId())) {
                return AjaxResult.error("此订单为非当前用户订单!");
            }
//            if(courseOrder.getOrderPlatform() == 9){
//                //问到课堂达人服务平台
//                return wxPrePay("wx1a9d23e331a63087", courseOrder, wendaoUser.getOpenId());
////                String appUrl = "https://store.wendao101.com/";
////                String clientIp = IpUtils.getIpAddr(request);
////                return wxWapPrePay("wx1a9d23e331a63087", courseOrder, clientIp, appUrl,"pc端知识商城");
//            }
            if(courseOrder.getOrderPlatform() == 5){
//                String appUrl = "https://wap.wendao101.com";
//                String clientIp = IpUtils.getIpAddr(request);
//                return wxWapPrePay("wx53bd355db2a7f7ac", courseOrder, clientIp, appUrl,"问到H5端");
                if(StringUtils.isNotBlank(createKuaishouPreOrderDTO.getUserMobile())){
                    redisService.setCacheObject(wx_h5_not_sync_user_mobile + courseOrder.getOrderId(), "no_sync",3L, TimeUnit.DAYS);
                    courseOrder.setBuyerUserMobile(createKuaishouPreOrderDTO.getUserMobile());
                    courseOrderService.updateCourseOrder(courseOrder);
                }
                return wxPrePay("wx53bd355db2a7f7ac", courseOrder, StringUtils.isNotBlank(createKuaishouPreOrderDTO.getOpenid())?createKuaishouPreOrderDTO.getOpenid():wendaoUser.getOpenId());
            }
            if(courseOrder.getOrderPlatform() == 8){
//                String appUrl = "https://wap.wendao101.com";
//                String clientIp = IpUtils.getIpAddr(request);
//                return wxWapPrePay("wx53bd355db2a7f7ac", courseOrder, clientIp, appUrl,"问到H5端");
                return wxPrePay("wx1a9d23e331a63087", courseOrder, StringUtils.isNotBlank(createKuaishouPreOrderDTO.getOpenid())?createKuaishouPreOrderDTO.getOpenid():wendaoUser.getOpenId());
            }
        }
        return AjaxResult.error("不支持的平台!");
    }


    @PostMapping("/createWendaoOrderGiveAwayManyOrder")
    public AjaxResult createWendaoOrderGiveAwayManyOrder(@RequestBody CreateWendaoOrderGiveManyOrderDTO createWendaoOrderDTO) {
        if(CollectionUtils.isEmpty(createWendaoOrderDTO.getCourseIds())){
            return AjaxResult.error("没有符合领取要求的课程!");
        }
        WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(createWendaoOrderDTO.getUserId());
        if (wendaoUser == null) {
            return AjaxResult.error("用户信息不存在,请检查userId");
        }
        if(StringUtils.isBlank(createWendaoOrderDTO.getPrimaryOrderId())){
            return AjaxResult.error("领取课程失败!主订单id必传");
        }
        CourseOrder primaryOrder = courseOrderService.selectCourseOrderByOrderId(createWendaoOrderDTO.getPrimaryOrderId());
        if(primaryOrder==null){
            return AjaxResult.error("领取课程失败!主订单不存在");
        }
        List<String> giveOrderIds = new ArrayList<>();
        // 问到好课的app
        for(Long courseId : createWendaoOrderDTO.getCourseIds()){
            CourseAudit courseAudit = courseAuditService.selectCourseAuditById(courseId);
            if (courseAudit == null) {
                System.out.println("课程信息不存在");
                continue;
            }
            //获取价格
            BigDecimal price = new BigDecimal(0);
            //创建订单
            CourseOrder courseOrder = new CourseOrder();
            String yyyyMMdd = DateUtils.dateTime();
            String orderId = yyyyMMdd + createWendaoOrderDTO.getUserId() + String.format("%04d", redisService.incr(yyyyMMdd, createWendaoOrderDTO.getUserId()));
            courseOrder.setOrderId(orderId);
            Integer courseDuration = courseAudit.getCourseDuration();
            courseOrder.setCourseDuration(courseDuration == null ? 0L : courseDuration.longValue());
            courseOrder.setCourseId(courseId);
            courseOrder.setTeacherId(courseAudit.getTeacherId());
            courseOrder.setCourseImgUrl(courseAudit.getCoverPicUrl());
            courseOrder.setValidity(courseAudit.getExpirationDay());
            courseOrder.setCourseTitle(courseAudit.getTitle());
            courseOrder.setCoursePrice(courseAudit.getPrice());
            courseOrder.setAppNameType(primaryOrder.getAppNameType());
            courseOrder.setPayPrice(price);
            //支付时间
            courseOrder.setPayTime(new Date());
            //非推广订单
            courseOrder.setIsPromoter(0);
            //9赠送课程订单
            courseOrder.setOrderType(9);
            //是否赠送课程
            courseOrder.setIsCourse(1);
            courseOrder.setOriginalPrice(courseAudit.getOriginalPrice());
            //直接设置为已支付
            courseOrder.setOrderStatus(1);
            courseOrder.setOrderTime(new Date());
            courseOrder.setFundsType(1);
            courseOrder.setOrderPlatform(createWendaoOrderDTO.getPlatform());
            courseOrder.setBuyerUserId(wendaoUser.getId());
            courseOrder.setBuyerUserImg(wendaoUser.getAvatarUrl());
            courseOrder.setBuyerUserName(wendaoUser.getNickName());
            courseOrder.setBuyerUserMobile(wendaoUser.getTelNumber());
            courseOrder.setMyEarningsPrice(price);
            int i = courseOrderService.insertCourseOrder(courseOrder);
            if(i>0){
                BuyCourseGiveOrder buyCourseGiveOrder = new BuyCourseGiveOrder();
                buyCourseGiveOrder.setTeacherId(courseOrder.getTeacherId());
                buyCourseGiveOrder.setOrderId(courseOrder.getOrderId());
                buyCourseGiveOrder.setOrderStatus(courseOrder.getOrderStatus());
                buyCourseGiveOrderService.insertBuyCourseGiveOrder(buyCourseGiveOrder);
                giveOrderIds.add(orderId);
                System.out.println("创建赠课订单成功!订单号:"+orderId);
            }else{
                System.out.println("创建赠课订单失败!订单号:"+orderId);
            }
        }
        if(CollectionUtils.isNotEmpty(giveOrderIds)){
            redisService.setCacheList(give_order_id_cache_list + createWendaoOrderDTO.getPrimaryOrderId(), giveOrderIds);
        }
        return AjaxResult.success("领取课程成功!");

    }


    /**
     * 赠送课程下单
     *
     * @param createWendaoOrderDTO
     * @return
     */
    @PostMapping("/createWendaoOrderGiveAway")
    public AjaxResult createWendaoOrderGiveAway(@RequestBody CreateWendaoOrderDTO createWendaoOrderDTO) {
        String wbbOrderId = createWendaoOrderDTO.getWbbOrderId();
        // 问到好课的app
        long courseId = createWendaoOrderDTO.getCourseId();
        CourseAudit courseAudit = courseAuditService.selectCourseAuditById(courseId);
        if (courseAudit == null) {
            return AjaxResult.error("课程信息不存在");
        }
        if(!StringUtils.equals("backend", createWendaoOrderDTO.getSourceFrom())) {
            int remainingCount = teacherResourcePackageService.sumRemainingCountByTeacherIdAndFeatureCode(courseAudit.getTeacherId(), "gift_course");
            //计算资源包数量,如果未计算则计算
            if (remainingCount <= 0) {
                return AjaxResult.error("资源包数量不足,请购买资源包!");
            }
        }
        //获取价格
        BigDecimal price = new BigDecimal(0);
        //创建订单
        CourseOrder courseOrder = new CourseOrder();
        WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(createWendaoOrderDTO.getUserId());
        if (wendaoUser == null) {
            return AjaxResult.error("用户信息不存在,请检查userId");
        }
        int platform = createWendaoOrderDTO.getPlatform();
        if (wendaoUser.getPlatform() == 1) {
            platform = 0;
        } else if (wendaoUser.getPlatform() == 2) {
            platform = 2;
        } else if (wendaoUser.getPlatform() == 3) {
            platform = 1;
        } else if (wendaoUser.getPlatform() == 4) {
            platform = 3;
        }
        createWendaoOrderDTO.setPlatform(platform);
        Long userId = wendaoUser.getId();
        String yyyyMMdd = DateUtils.dateTime();
        String orderId = yyyyMMdd + userId + String.format("%04d", redisService.incr(yyyyMMdd, userId));
        if (StringUtils.isNotBlank(wbbOrderId)) {
            CourseOrder courseOrderWBB = courseOrderService.selectCourseOrderByOrderId(wbbOrderId);
            if (courseOrderWBB != null) {
                return AjaxResult.error("创建赠课订单失败!");
            }
            orderId = wbbOrderId;
        }
        courseOrder.setOrderId(orderId);
        Integer courseDuration = courseAudit.getCourseDuration();
        courseOrder.setCourseDuration(courseDuration == null ? 0L : courseDuration.longValue());
        courseOrder.setCourseId(courseId);
        courseOrder.setTeacherId(courseAudit.getTeacherId());
        courseOrder.setCourseImgUrl(courseAudit.getCoverPicUrl());
        courseOrder.setValidity(courseAudit.getExpirationDay());
        courseOrder.setCourseTitle(courseAudit.getTitle());
        courseOrder.setCoursePrice(courseAudit.getPrice());
        courseOrder.setAppNameType(wendaoUser.getAppNameType());
        courseOrder.setPayPrice(price);
        //支付时间
        courseOrder.setPayTime(new Date());
        //非推广订单
        courseOrder.setIsPromoter(0);
        //9赠送课程订单
        courseOrder.setOrderType(9);
        //是否赠送课程
        courseOrder.setIsCourse(1);
        courseOrder.setOriginalPrice(courseAudit.getOriginalPrice());
        //直接设置为已支付
        courseOrder.setOrderStatus(1);
        courseOrder.setOrderTime(new Date());
        courseOrder.setFundsType(1);
        courseOrder.setOrderPlatform(createWendaoOrderDTO.getPlatform());
        courseOrder.setBuyerUserId(wendaoUser.getId());
        courseOrder.setBuyerUserImg(wendaoUser.getAvatarUrl());
        courseOrder.setBuyerUserName(wendaoUser.getNickName());
        courseOrder.setBuyerUserMobile(wendaoUser.getTelNumber());
        courseOrder.setMyEarningsPrice(price);
        if (!StringUtils.equals("backend", createWendaoOrderDTO.getSourceFrom())) {
            boolean isSuccess = teacherResourcePackageService.deductResourcePackageCount(courseAudit.getTeacherId(), "gift_course");
            if (isSuccess) {
                int i = courseOrderService.insertCourseOrder(courseOrder);
                if (i > 0) {
                    return AjaxResult.success("创建赠课订单成功!", orderId);
                } else {
                    return AjaxResult.error("创建赠课订单失败!");
                }
            } else {
                return AjaxResult.error("资源包数量不足,请购买资源包!");
            }
        } else {
            int i = courseOrderService.insertCourseOrder(courseOrder);
            if (i > 0) {
                return AjaxResult.success("创建赠课订单成功!", orderId);
            } else {
                return AjaxResult.error("创建赠课订单失败!");
            }
        }
    }

    /**
     * 微信外支付
     * @param createKuaishouPreOrderDTO
     * @param request
     * @return
     */
    @PostMapping("/createWxWapOrder")
    public AjaxResult createWapOrder(@RequestBody CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO, HttpServletRequest request) {
        String orderId = createKuaishouPreOrderDTO.getOrderId();
        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
        if(courseOrder==null){
            return AjaxResult.error("订单不存在");
        }
        if(createKuaishouPreOrderDTO.getUserId()==null){
            return AjaxResult.error("用户id必传");
        }
        if (courseOrder.getOrderPlatform() == 9||courseOrder.getOrderPlatform() == 5||courseOrder.getOrderPlatform() == 8) {
            WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(createKuaishouPreOrderDTO.getUserId());
            if (wendaoUser == null) {
                return AjaxResult.error("用户不存在");
            }
            if (!Objects.equals(courseOrder.getBuyerUserId(),wendaoUser.getId())) {
                return AjaxResult.error("此订单为非当前用户订单!");
            }
            if(courseOrder.getOrderPlatform() == 8){
                String appUrl = "https://wap.wendao101.com/";
                String clientIp = IpUtils.getIpAddr(request);
                return wxWapPrePay("wx1a9d23e331a63087", courseOrder, clientIp, appUrl,"知识店铺");
            }
            if(courseOrder.getOrderPlatform() == 9){
                String appUrl = "https://store.wendao101.com/";
                String clientIp = IpUtils.getIpAddr(request);
                return wxWapPrePay("wx1a9d23e331a63087", courseOrder, clientIp, appUrl,"pc端知识商城");
            }
            if(courseOrder.getOrderPlatform() == 5){
                String appUrl = "https://wap.wendao101.com";
                String clientIp = IpUtils.getIpAddr(request);
                return wxWapPrePay("wx53bd355db2a7f7ac", courseOrder, clientIp, appUrl,"问到H5端");
            }
        }
        return AjaxResult.error("不支持的平台!");
    }

    /**
     * 快手预下单接口
     *
     * @param createKuaishouPreOrderDTO
     * @return AjaxResult
     */
    @PostMapping("/createOrder")
    public AjaxResult createOrder(@RequestBody CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO, HttpServletRequest request) {
        String userAgent = request.getHeader("user-agent");
        boolean isWxBrowser = StringUtils.isNotBlank(userAgent) && userAgent.toLowerCase().contains("micromessenger");
        //判断订单平台
        String orderId = createKuaishouPreOrderDTO.getOrderId();
        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
        if(courseOrder==null){
            return AjaxResult.error("订单不存在");
        }
        if (createKuaishouPreOrderDTO.getUserId() != null && courseOrder.getOrderPlatform() == 8) {
            WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(createKuaishouPreOrderDTO.getUserId());
            if (wendaoUser == null) {
                return AjaxResult.error("用户不存在");
            }
            if (!Objects.equals(courseOrder.getBuyerUserId(),wendaoUser.getId())) {
                return AjaxResult.error("此订单为非当前用户订单!");
            }
            if (isWxBrowser) {
                if (StringUtils.isBlank(wendaoUser.getOpenId())) {
                    return AjaxResult.error("请先绑定微信");
                }
                //问到课堂达人服务平台
                return wxPrePay("wx1a9d23e331a63087", courseOrder, wendaoUser.getOpenId());
            } else {
                TeacherDTO teacherDTO = courseOrderService.selectTeacherByTeacherId(courseOrder.getTeacherId());
                String appUrl = teacherDTO == null ? null : teacherDTO.getKnowledgeStoreDomain();
                appUrl = StringUtils.isNotBlank(appUrl) ? "https://" + appUrl : null;
                String clientIp = IpUtils.getIpAddr(request);
                return wxH5PrePay("wx1a9d23e331a63087", courseOrder, clientIp, appUrl);
            }
        }
        //其他平台
        String openid = createKuaishouPreOrderDTO.getOpenid();
        WendaoUser wendaoUser = wendaoUserService.selectWendaoUserByOpenid(openid, createKuaishouPreOrderDTO.getAppNameType());
        if(wendaoUser==null){
            return AjaxResult.error("用户不存在");
        }
        if (!Objects.equals(courseOrder.getBuyerUserId(),wendaoUser.getId())) {
            return AjaxResult.error("此订单为非当前用户订单!");
        }
        // 问到好课
        // 判断是快手还是微信
        if (courseOrder.getAppNameType() == 1) {
            //快手支付
            if (courseOrder.getOrderPlatform() == 2) {
                //问到好课快手基础信息
                String accessToken = ksAccessTokenService.getAccessToken();
                String appId = kuaishouConfig.getAppid();
                String appSecret = kuaishouConfig.getSecret();
                String payNotifyUrl = kuaishouConfig.getPayNotifyUrl();
                return ksPrePay(openid, courseOrder, appId, appSecret, accessToken, payNotifyUrl, 1297);
            }
            //好课微信支付
            if (courseOrder.getOrderPlatform() == 1) {
                //获取好课的appid
                String appId = wendaoWxConfig.getAppId();
                return wxPrePay(appId, courseOrder, openid);
            }
        }
        //问到课堂
        if (courseOrder.getAppNameType() == 2) {
            //快手支付
            if (courseOrder.getOrderPlatform() == 2) {
                if (ktKuaishouConfig.getAppid().equals(createKuaishouPreOrderDTO.getAppId())) {
                    //问到课堂快手基础信息
                    String accessToken = ksAccessTokenService.getktAccessToken(createKuaishouPreOrderDTO.getAppId());
                    String appId = ktKuaishouConfig.getAppid();
                    String appSecret = ktKuaishouConfig.getSecret();
                    String payNotifyUrl = ktKuaishouConfig.getPayNotifyUrl();
                    return ksPrePay(openid, courseOrder, appId, appSecret, accessToken, payNotifyUrl, 1298);
                } else if (zkKuaishouConfig.getAppid().equals(createKuaishouPreOrderDTO.getAppId())) {
                    //问到智库快手基础信息
                    String accessToken = ksAccessTokenService.getktAccessToken(createKuaishouPreOrderDTO.getAppId());
                    String appId = zkKuaishouConfig.getAppid();
                    String appSecret = zkKuaishouConfig.getSecret();
                    String payNotifyUrl = zkKuaishouConfig.getPayNotifyUrl();
                    return ksPrePay(openid, courseOrder, appId, appSecret, accessToken, payNotifyUrl, 1299);
                }

            }
            //课堂微信支付
            if (courseOrder.getOrderPlatform() == 1) {
                //获取课堂的appid
                String appId = wendaoWxConfig.getKtAppId();
                return wxPrePay(appId, courseOrder, openid);
            }
        }
        return AjaxResult.error("来源app不支持支付");
    }


    @PostMapping("/createAlipayH5Order")
    private AjaxResult createAlipayH5Order(@RequestBody CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO){
        String orderId = createKuaishouPreOrderDTO.getOrderId();
        Long userId = createKuaishouPreOrderDTO.getUserId();
        if(StringUtils.isBlank(orderId)||userId==null){
            return AjaxResult.error("参数错误");
        }
        WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(userId);
        if(wendaoUser==null){
            return AjaxResult.error("用户不存在");
        }
        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderIdAndBuyUserId(userId, orderId);
        if (courseOrder == null) {
            return AjaxResult.error("订单不存在");
        }
        String returnUrl = createKuaishouPreOrderDTO.getReturnUrl();
        if(StringUtils.isNotBlank(returnUrl)){
            try {
                returnUrl = URLDecoder.decode(returnUrl, "utf-8");
                redisService.setCacheObject(alipayRedirectionUrlKey+courseOrder.getOrderId(),returnUrl,60L, TimeUnit.MINUTES);
                System.out.println("returnUrl:"+returnUrl);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        }else{
            returnUrl = "";
        }
        String result  = aliPayService.h5Pay(courseOrder,returnUrl);
        if(StringUtils.equals(result, "系统错误")){
            return AjaxResult.error("系统错误");
        }else{
            Map<String,String> h5UrlMap = new HashMap<>();
            h5UrlMap.put("alipayH5Url",result);
            return AjaxResult.success(h5UrlMap);
        }
    }

    @PostMapping("/createAlipayPcOrder")
    private AjaxResult createAlipayPcOrder(@RequestBody CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO){
        String orderId = createKuaishouPreOrderDTO.getOrderId();
        Long userId = createKuaishouPreOrderDTO.getUserId();
        if(StringUtils.isBlank(orderId)||userId==null){
            return AjaxResult.error("参数错误");
        }
        WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(userId);
        if(wendaoUser==null){
            return AjaxResult.error("用户不存在");
        }
        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderIdAndBuyUserId(userId, orderId);
        if (courseOrder == null) {
            return AjaxResult.error("订单不存在");
        }
        String returnUrl = createKuaishouPreOrderDTO.getReturnUrl();
        if(StringUtils.isNotBlank(returnUrl)){
            try {
                returnUrl = URLDecoder.decode(returnUrl, "utf-8");
                redisService.setCacheObject(alipayRedirectionUrlKey+courseOrder.getOrderId(),returnUrl,60L, TimeUnit.MINUTES);
                System.out.println("returnUrl:"+returnUrl);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        }else{
            returnUrl = "";
        }
        String result  = aliPayService.pcPay(courseOrder,returnUrl);
        if(StringUtils.equals(result, "系统错误")){
            return AjaxResult.error("系统错误");
        }else{
            return AjaxResult.success("调用支付成功!",result);
        }
    }

    @NotNull
    private AjaxResult ksPrePay(String openid, CourseOrder courseOrder, String appId, String appSecret, String accessToken, String payNotifyUrl, int type) {
        //结束
        CreateOrderDTO createOrderDTOSubmit = new CreateOrderDTO();
        createOrderDTOSubmit.setOpen_id(openid);
        createOrderDTOSubmit.setOut_order_no(courseOrder.getOrderId());
        createOrderDTOSubmit.setTotal_amount(BaseWxPayRequest.yuan2Fen(courseOrder.getPayPrice()));
        createOrderDTOSubmit.setSubject(courseOrder.getCourseTitle());
        createOrderDTOSubmit.setType(type);
        createOrderDTOSubmit.setDetail(courseOrder.getCourseTitle());
        createOrderDTOSubmit.setExpire_time(900);
        createOrderDTOSubmit.setNotify_url(payNotifyUrl);
        //创建时间2025年7月31日0点0分0秒
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, 2025);
        calendar.set(Calendar.MONTH, 6);
        calendar.set(Calendar.DAY_OF_MONTH, 28);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date time = calendar.getTime();
        Date now  = new Date();
        boolean isNowAfter = now.after(time);

        //新增一个字段goods_id
        boolean isContainGoodsId = false;
        if (courseOrder.getCourseId() != null && isNowAfter) {
            isContainGoodsId = true;
            //先查出课程
            CourseAudit courseAudit = courseAuditService.selectCourseAuditById(courseOrder.getCourseId());
            if (courseAudit == null) {
                return AjaxResult.error("课程不存在");
            }
            if (StringUtils.isBlank(courseAudit.getKsCourseId())) {
                return AjaxResult.error("课程未审核通过或在审核中!");
            }
            createOrderDTOSubmit.setGoods_id(courseAudit.getKsCourseId());
        }
        createOrderDTOSubmit.setSign(createOrderDTOSubmit.createSign(appId, appSecret, isContainGoodsId));
        CreateOrderResult createOrderResult = kuaishouPayService.createOrder(appId, accessToken, createOrderDTOSubmit);
        if (createOrderResult.getResult() == 1) {
            //同时同步订单到快手小程序
            try {
                //需要先同步订单
                syncKsOrder(courseOrder, createOrderResult.getOrder_info().getOrder_no(), appId, accessToken, 1);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return AjaxResult.success(createOrderResult.getOrder_info());
        } else {
            return AjaxResult.error(createOrderResult.getError_msg());
        }
    }

    /**
     * 微信小程序jsApi预下单
     *
     * @param appId
     * @param courseOrder
     * @param openid
     * @return
     */
    private AjaxResult wxPrePay(String appId, CourseOrder courseOrder, String openid) {
        String notifyUrl = wendaoWxConfig.getPayNotifyUrl();
        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        request.setAppid(appId);
        request.setOutTradeNo(courseOrder.getOrderId());
        request.setNotifyUrl(notifyUrl);
        request.setDescription(courseOrder.getCourseTitle());

        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(openid);
        request.setPayer(payer);

        //构建金额信息
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        //设置币种信息
        amount.setCurrency(WxPayConstants.CurrencyType.CNY);
        //设置金额
        amount.setTotal(BaseWxPayRequest.yuan2Fen(courseOrder.getPayPrice()));
        request.setAmount(amount);

        try {
            WxPayUnifiedOrderV3Result.JsapiResult result = wxPayService.createOrderV3(TradeTypeEnum.JSAPI, request);
            return AjaxResult.success(result);
        } catch (WxPayException e) {
            return AjaxResult.error(e.getReturnMsg());
        }
    }

    /**
     * 微信h5支付
     * @param appId appid
     * @param courseOrder 订单信息
     * @param clientIp 客户端ip
     * @param appUrl 应用的url
     * @return
     */
    private AjaxResult wxH5PrePay(String appId, CourseOrder courseOrder,String clientIp,String appUrl) {
        /**
         * 回调地址
         */
        String notifyUrl = wendaoWxConfig.getPayNotifyUrl();
        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        request.setAppid(appId);
        request.setDescription(courseOrder.getCourseTitle());
        request.setOutTradeNo(courseOrder.getOrderId());
        request.setNotifyUrl(notifyUrl);


        WxPayUnifiedOrderV3Request.SceneInfo sceneInfo = new WxPayUnifiedOrderV3Request.SceneInfo();
        sceneInfo.setPayerClientIp(clientIp);
        WxPayUnifiedOrderV3Request.H5Info h5Info= new WxPayUnifiedOrderV3Request.H5Info();
        h5Info.setType("Wap");
        h5Info.setAppName("问到知识店铺");
        h5Info.setAppUrl(appUrl);

        sceneInfo.setH5Info(h5Info);
        request.setSceneInfo(sceneInfo);
        //构建金额信息
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        //设置币种信息
        amount.setCurrency(WxPayConstants.CurrencyType.CNY);
        //设置金额
        amount.setTotal(BaseWxPayRequest.yuan2Fen(courseOrder.getPayPrice()));
        request.setAmount(amount);

        try {
            //会返回h5_url,这个url为拉起h5支付的
            String  h5Url = wxPayService.createOrderV3(TradeTypeEnum.H5, request);
            return AjaxResult.success("请求成功!",h5Url);
        } catch (WxPayException e) {
            return AjaxResult.error(e.getReturnMsg());
        }
    }

    /**
     * 微信h5支付,wap版本
     * @param appId appid
     * @param courseOrder 订单信息
     * @param clientIp 客户端ip
     * @param appUrl 应用的url
     * @return
     */
    private AjaxResult wxWapPrePay(String appId, CourseOrder courseOrder,String clientIp,String appUrl,String appName) {
        /**
         * 回调地址
         */
        String notifyUrl = wendaoWxConfig.getPayNotifyUrl();
        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        request.setAppid(appId);
        request.setDescription(courseOrder.getCourseTitle());
        request.setOutTradeNo(courseOrder.getOrderId());
        request.setNotifyUrl(notifyUrl);


        WxPayUnifiedOrderV3Request.SceneInfo sceneInfo = new WxPayUnifiedOrderV3Request.SceneInfo();
        sceneInfo.setPayerClientIp(clientIp);
        WxPayUnifiedOrderV3Request.H5Info h5Info= new WxPayUnifiedOrderV3Request.H5Info();
        h5Info.setType("Wap");
        h5Info.setAppName(appName);
        h5Info.setAppUrl(appUrl);

        sceneInfo.setH5Info(h5Info);
        request.setSceneInfo(sceneInfo);
        //构建金额信息
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        //设置币种信息
        amount.setCurrency(WxPayConstants.CurrencyType.CNY);
        //设置金额
        amount.setTotal(BaseWxPayRequest.yuan2Fen(courseOrder.getPayPrice()));
        request.setAmount(amount);

        try {
            //会返回h5_url,这个url为拉起h5支付的
            String h5Url = wxPayService.createOrderV3(TradeTypeEnum.H5, request);
            return AjaxResult.success("请求成功!",h5Url);
        } catch (WxPayException e) {
            return AjaxResult.error(e.getReturnMsg());
        }
    }

    @PostMapping("/createWendaoRefund")
    public AjaxResult createWendaoRefund(@RequestBody CreateWendaoRefundDTO createWendaoRefundDTO) {
        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(createWendaoRefundDTO.getOrderId());
        if (courseOrder == null) {
            return AjaxResult.error("订单不存在");
        }
        WendaoUser wendaoUser = null;
        if (courseOrder.getOrderPlatform() != 8 && StringUtils.isNotBlank(createWendaoRefundDTO.getOpenid())) {
            wendaoUser = wendaoUserService.selectWendaoUserByOpenid(createWendaoRefundDTO.getOpenid(), courseOrder.getAppNameType());
        } else {
            Long userId = createWendaoRefundDTO.getUserId();
            if(!Objects.equals(userId,courseOrder.getBuyerUserId())){
                return AjaxResult.error("订单不属于当前用户!");
            }
            wendaoUser = wendaoUserService.selectWendaoUserById(courseOrder.getBuyerUserId());
        }
        if(wendaoUser==null){
            return AjaxResult.error("用户不存在");
        }
        Long wendaoUserId = wendaoUser.getId();
        if (!Objects.equals(wendaoUserId, courseOrder.getBuyerUserId())) {
            return AjaxResult.error("订单与用户不匹配");
        }
        List<CourseRefund> courseRefunds = courseRefundService.selectCourseRefundRefusedByOrderId(createWendaoRefundDTO.getOrderId());
        if (CollectionUtils.isNotEmpty(courseRefunds)) {
            return AjaxResult.error("之前的订单退款申请已被拒绝,拒绝原因:" + courseRefunds.get(0).getRefusalOfRefund());
        }
        CourseRefund courseRefund1 = courseRefundService.selectCourseRefundNotHandledByOrderId(createWendaoRefundDTO.getOrderId());
        if (courseRefund1 != null) {
            return AjaxResult.error("之前的订单退款申请还未处理完成,请稍后查看.");
        }
        CourseRefund courseRefundHandled = courseRefundService.selectCourseRefundHandledByOrderId(createWendaoRefundDTO.getOrderId());
        if (courseRefundHandled != null) {
            return AjaxResult.error("您的退款请求已处理,请不要重复提交.");
        }
        //创建本侧退款单
        CourseRefund courseRefund = new CourseRefund();
        courseRefund.setBuyerUserId(wendaoUserId);
        courseRefund.setOrderId(courseOrder.getOrderId());
        courseRefund.setRefundPrice(courseOrder.getPayPrice());
        courseRefund.setRefundReason(createWendaoRefundDTO.getRefundReason());
        courseRefund.setSupplimentalDescription(createWendaoRefundDTO.getSupplimentalDescription());
        courseRefund.setReceiptImg(createWendaoRefundDTO.getReceiptImg());
        courseRefund.setIsDelete(0);
        //需要退款审核
        courseRefund.setRefundStatus(0);
        courseRefund.setRefundType(0);
        //抖音逻辑是独立的
        //微信
        if (courseOrder.getOrderPlatform() == 1) {
            //退款订单的微信是3
            courseRefund.setRefundPlatform(3);
        }
        //快手
        if (courseOrder.getOrderPlatform() == 2) {
            //退款订单的快手是2
            courseRefund.setRefundPlatform(2);
        }
        //知识店铺
        if (courseOrder.getOrderPlatform() == 8) {
            //知识店铺是8
            courseRefund.setRefundPlatform(8);
        }
        //设置来源app,问到课堂或问到好课
        courseRefund.setAppNameType(courseOrder.getAppNameType());
        //设置来源
        String yyyyMMdd = DateUtils.dateTime();
        String refundId = yyyyMMdd + wendaoUserId + String.format("%04d", redisService.incr(yyyyMMdd, wendaoUserId));
        courseRefund.setRefundId(refundId);
        Long courseId = courseOrder.getCourseId();
        String courseTitle = courseOrder.getCourseTitle();
        courseRefund.setCourseId(courseId);
        courseRefund.setCourseTitle(courseTitle);
        courseRefund.setCoursePrice(courseOrder.getCoursePrice());
        courseRefund.setOriginalPrice(courseOrder.getOriginalPrice());
        courseRefund.setCourseImgUrl(courseOrder.getCourseImgUrl());
        courseRefund.setBuyerUserName(wendaoUser.getUserName());
        courseRefund.setBuyerUserMobile(wendaoUser.getTelNumber());
        courseRefund.setRefundTime(new Date());
        courseRefund.setTeacherId(courseOrder.getTeacherId());
        courseRefund.setOrderTime(courseOrder.getPayTime());
        courseRefund.setUpdateTime(new Date());

        courseRefund.setCourseDuration(courseOrder.getCourseDuration());
        courseRefund.setStudyDuration(courseOrder.getStudyDuration());

        Integer isPromoter = courseOrder.getIsPromoter();
        if (isPromoter == 1) {
            //推广订单
            courseRefund.setPromoterId(courseOrder.getPromoterId());
            courseRefund.setPromoterName(courseOrder.getPromoterName());
            courseRefund.setPromoterMobile(courseOrder.getPromoterMobile());
        }
        courseRefundService.insertCourseRefund(courseRefund);

        courseOrder.setOrderStatus(4);
        courseOrderService.updateCourseOrder(courseOrder);

        //退款通知
        MessageNotification messageNotification = new MessageNotification();
        messageNotification.setId(courseOrder.getBuyerUserId());
        messageNotification.setTeacherId(courseOrder.getTeacherId());
        messageNotification.setUserId(null);
        messageNotification.setNoticeTitle("「 退款通知 」 用户购买的课程《" + courseOrder.getCourseTitle() + "》发起了退款");
        String userName = "用户" + courseOrder.getBuyerUserId();
        userName = StringUtils.isBlank(courseOrder.getBuyerUserName()) ? userName : courseOrder.getBuyerUserName();
        messageNotification.setNoticeContent("用户昵称：" + userName + "\\n" + "退款金额：" + courseOrder.getPayPrice() + "\\n" + "课程名称：" + courseOrder.getCourseTitle());
        messageNotification.setNoticeType(7);
        messageNotification.setIsDelete(0);
        messageNotification.setReadStatus(0);
        messageNotification.setCreateTime(DateUtils.getNowDate());
        messageNotification.setUpdateTime(DateUtils.getNowDate());
        messageNotificationService.insertMessageNotification(messageNotification);
        Map<String, String> map = new HashMap<>();
        map.put("refundId", refundId);
        return AjaxResult.success(map);
    }

    /**
     * 查询退款
     *
     * @param queryRefundDTO
     * @return
     */
    @PostMapping("/queryRefund")
    public AjaxResult queryRefund(@RequestBody QueryRefundDTO queryRefundDTO) {
        String access_token = ksAccessTokenService.getAccessToken();
        QueryRefundResult queryRefundResult = kuaishouPayService.queryRefund(kuaishouConfig.getAppid(), access_token, queryRefundDTO);
        return AjaxResult.success(queryRefundResult);
    }

    /**
     * 结算
     *
     * @param settleDTO
     * @return
     */
    @PostMapping("/settle")
    public AjaxResult settle(@RequestBody SettleDTO settleDTO) {
        String access_token = ksAccessTokenService.getAccessToken();
        SettleResult settleResult = kuaishouPayService.settle(kuaishouConfig.getAppid(), access_token, settleDTO);
        return AjaxResult.success(settleResult);
    }

    /**
     * 查询结算
     *
     * @param querySettleDTO
     * @return
     */
    @PostMapping("/querySettle")
    public AjaxResult querySettle(@RequestBody QuerySettleDTO querySettleDTO) {
        String access_token = ksAccessTokenService.getAccessToken();
        QuerySettleResult querySettleResult = kuaishouPayService.querySettle(kuaishouConfig.getAppid(), access_token, querySettleDTO);
        return AjaxResult.success(querySettleResult);
    }

    /**
     * 支付回调
     *
     * @param payCallbackDTO
     * @return
     */
    @PostMapping("/ksPayCallback")
    public PayCallbackResult ksPayCallback(@RequestBody PayCallbackDTO payCallbackDTO) {
        PayCallbackResult payCallbackResult = new PayCallbackResult();
        payCallbackResult.setResult(1);
        payCallbackResult.setMessage_id(payCallbackDTO.getMessage_id());
        String appId = payCallbackDTO.getApp_id();
        //如果是支付
        if (payCallbackDTO.getBiz_type().equals("PAYMENT")) {
            CallBackData data = payCallbackDTO.getData();
            String orderId = data.getOut_order_no();
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
            KuishouPayStatus kuishouPayStatus = KuishouPayStatus.getEnum(data.getStatus());
            if (kuishouPayStatus == KuishouPayStatus.SUCCESS) {
                //支付方式  支付宝支付 微信支付 银行卡支付
                //ks支付渠道。取值：UNKNOWN - 未知｜WECHAT-微信｜ALIPAY-支付宝
                KuishouPayWay kuishouPayWay = KuishouPayWay.getEnum(data.getChannel());
                courseOrder.setPayWay(kuishouPayWay.getWendaoName());
                courseOrder.setTradingOrderNumber(data.getTrade_no());
                courseOrder.setOutOrderNumber(data.getKs_order_no());
                courseOrder.setPayTime(new Date());
                //支付成功
                //判断和订单金额是否相等
                int orderAmount = data.getOrder_amount();
                int wendaoOrderAmount = courseOrder.getPayPrice().multiply(new BigDecimal(100)).intValue();
                if (orderAmount == wendaoOrderAmount) {
                    courseOrder.setOrderStatus(1);
                    courseOrderService.updateCourseOrder(courseOrder);
                    /**
                     * 秒杀逻辑
                     */
                    SecondKill secondKillUsed = redisService.getCacheObject(WendaoRedisKey.WENDAO_SECOND_KILL_KS_WX_ORDER_KEY + orderId);
                    if (secondKillUsed != null) {
                        //秒杀数量加1
                        //秒杀计数
                        long count = redisService.secKillIncr(secondKillUsed.getId());
                        //更新redis中的值
                        String key = WendaoRedisKey.WENDAO_SECOND_KILL_KEY + secondKillUsed.getCourseId();
                        List<SecondKill> cacheList = redisService.getCacheList(key);
                        if (CollectionUtils.isNotEmpty(cacheList)) {
                            long index = -1;
                            for (int i = 0; i < cacheList.size(); i++) {
                                if (Objects.equals(cacheList.get(i).getId(), secondKillUsed.getId())) {
                                    index = i;
                                    break;
                                }
                            }
                            if (index >= 0) {
                                if (count >= secondKillUsed.getSeckillNum().longValue()) {
                                    secondKillUsed.setSeckillStatus(1);
                                    //先更新redis
                                    redisService.lset(key, index, secondKillUsed);
                                    SecondKill secondKillUpdate = new SecondKill();
                                    secondKillUpdate.setId(secondKillUsed.getId());
                                    secondKillUpdate.setSeckillStatus(1);
                                    secondKillService.updateSecondKillNoUpdateTime(secondKillUpdate);
                                }
                            }
                        }
                        String orderKey = WendaoRedisKey.WENDAO_SECOND_KILL_FOR_ORDER_KEY + courseOrder.getOrderId();
                        redisService.setCacheObject(orderKey, "success");
                        //更新客户端信息
                        sendMns(courseOrder.getCourseId());
                    }
                    //将优惠券设置为已使用
                    ReceiveCouponInfo receiveCouponInfo = redisService.getCacheObject(REDIS_COUPON_KEY_PREFIX + orderId);
                    if (receiveCouponInfo != null && receiveCouponInfo.getReceiveCouponId() > 0) {
                        if (receiveCouponInfo.getType() == 0) {
                            MReceiveCoupon mReceiveCoupon = receiveCouponService.selectMReceiveCouponById(receiveCouponInfo.getReceiveCouponId());
                            mReceiveCoupon.setStatus(1);
                            receiveCouponService.updateMReceiveCoupon(mReceiveCoupon);
                        } else if (receiveCouponInfo.getType() == 1) {
                            receiveCouponService.updateMDiscountsCodeMap(receiveCouponInfo.getReceiveCouponId());
                        }
                    }
                    //创建流水
                    wendaoSettlementService.createFundsRecord(courseOrder,null);
                    try {
                        String accessToken = ksAccessTokenService.getKsAccessTokenByAppNameType(courseOrder.getAppNameType(), appId);
                        syncKsOrder(courseOrder, data.getKs_order_no(), appId, accessToken, 11);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //发送短信通知
                    return payCallbackResult;
                } else {
                    //金额不等,订单不做处理
                    //返回失败
                    payCallbackResult.setResult(500);
                    return payCallbackResult;
                    //订单状态不变
                }
            }
            if (kuishouPayStatus == KuishouPayStatus.FAILED) {
                //待支付
                courseOrder.setOrderStatus(0);
                courseOrderService.updateCourseOrder(courseOrder);
                return payCallbackResult;
            }
            if (kuishouPayStatus == KuishouPayStatus.PROCESSING) {
                //处理中不做处理,后面查询订单状态
                System.out.println("处理中不做处理,后面查询订单状态");
            }
        }

        return payCallbackResult;
    }

    /**
     * 快手结算回调
     *
     * @param settleCallbackDTO
     * @return
     */
    @PostMapping("/ksSettleCallback")
    public PayCallbackResult ksSettleCallback(@RequestBody SettleCallbackDTO settleCallbackDTO) {
        PayCallbackResult payCallbackResult = new PayCallbackResult();
        payCallbackResult.setResult(1);
        payCallbackResult.setMessage_id(settleCallbackDTO.getMessage_id());
        if (settleCallbackDTO.getBiz_type().equals("SETTLE")) {
            //发起结算的时候就是使用问到的orderId作为结算Id,小程序唯一
            String orderId = settleCallbackDTO.getData().getOut_settle_no();
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
            if (courseOrder != null) {
                if (StringUtils.equals("SUCCESS", settleCallbackDTO.getData().getStatus())) {
                    //老师资/推广员资金收益
                    fundIncomeService.updateFundIncomeAndAccountPriceType(courseOrder.getOrderId());
                    promoterFundIncomeService.updatePromoterFundIncomeAndAccountPriceType(courseOrder.getOrderId());
                    //老师资/推广员流水记录
                    teacherFlowRecordService.updateTeacherFlowRecordAndAccountPriceType(courseOrder.getOrderId());
                    promoterFlowRecordService.updatePromoterFlowRecordAndAccountPriceType(courseOrder.getOrderId());
                    //订单信息,订单设置为已分账
                    CourseOrder courseOrderUpdate = new CourseOrder();
                    courseOrderUpdate.setId(courseOrder.getId());
                    courseOrderUpdate.setSettleStatus(1);
                    courseOrderService.updateCourseOrder(courseOrderUpdate);
                } else if (StringUtils.equals("FAILED", settleCallbackDTO.getData().getStatus())) {
                    //失败情况下订单信息,订单设置为未分账,原因是定时器里发起结算时这个值已经设置过了
                    CourseOrder courseOrderUpdate = new CourseOrder();
                    courseOrderUpdate.setId(courseOrder.getId());
                    courseOrderUpdate.setSettleStatus(0);
                    courseOrderService.updateCourseOrder(courseOrderUpdate);
                }

            }
        }
        return payCallbackResult;
    }


    @PostMapping("/wxPayCallback")
    public String wxPayCallback(HttpServletRequest request, HttpServletResponse response) {
        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String serialNo = request.getHeader("Wechatpay-Serial");
        String signature = request.getHeader("Wechatpay-Signature");
        log.info("请求头参数为：timestamp:{} nonce:{} serialNo:{} signature:{}", timestamp, nonce, serialNo, signature);
        if (StringUtils.isAnyBlank(timestamp, nonce, serialNo, signature)) {
            return WxPayNotifyV3Response.fail("头部参数错误!");
        }
        WxPayNotifyV3Result wxPayOrderNotifyV3Result = null;
        try {
            wxPayOrderNotifyV3Result = wxPayService.parseOrderNotifyV3Result(RequestUtils.readData(request),
                    new SignatureHeader(timestamp, nonce, signature, serialNo));
        } catch (WxPayException e) {
            log.error(e.getReturnMsg());
            return WxPayNotifyV3Response.fail("系统处理异常!");
        }
        //处理业务逻辑
        if (wxPayOrderNotifyV3Result != null && wxPayOrderNotifyV3Result.getResult() != null) {
            WxPayNotifyV3Result.DecryptNotifyResult result = wxPayOrderNotifyV3Result.getResult();
            {
                String orderId = result.getOutTradeNo();
                String tradeState = result.getTradeState();
                //支付成功处理
                if ("SUCCESS".equals(tradeState)) {
                    CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
                    courseOrder.setPayWay("微信支付");
                    courseOrder.setTradingOrderNumber(result.getTransactionId());
                    courseOrder.setOutOrderNumber(result.getTransactionId());
                    DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
                    ZonedDateTime zonedDateTime = ZonedDateTime.parse(result.getSuccessTime(), formatter);
                    // 转换为Date
                    Date date = Date.from(zonedDateTime.toInstant());
                    courseOrder.setPayTime(date);

                    //不应该用用户支付总金额,用支付总额
                    //有可能用了优惠,则存一份到redis期限是15天
                    Integer total = result.getAmount().getTotal();
                    Integer payerTotal = result.getAmount().getPayerTotal();
                    if (!total.equals(payerTotal)) {
                        WxPayRedisDTO wxPayRedisDTO = new WxPayRedisDTO();
                        wxPayRedisDTO.setPayerTotal(payerTotal);
                        wxPayRedisDTO.setTotal(total);
                        redisService.setCacheObject(WX_PAY_USER_DATA + orderId, wxPayRedisDTO, 15L, TimeUnit.DAYS);
                    }
                    Integer orderTotal = BaseWxPayRequest.yuan2Fen(courseOrder.getPayPrice());
                    if (total.intValue() == orderTotal.intValue()) {
                        courseOrder.setOrderStatus(1);
                        courseOrderService.updateCourseOrder(courseOrder);
                        /**
                         * 秒杀逻辑
                         */
                        SecondKill secondKillUsed = redisService.getCacheObject(WendaoRedisKey.WENDAO_SECOND_KILL_KS_WX_ORDER_KEY + orderId);
                        if (secondKillUsed != null) {
                            //秒杀数量加1
                            long count = redisService.secKillIncr(secondKillUsed.getId());
                            //更新redis中的值
                            String key = WendaoRedisKey.WENDAO_SECOND_KILL_KEY + secondKillUsed.getCourseId();
                            List<SecondKill> cacheList = redisService.getCacheList(key);
                            if (CollectionUtils.isNotEmpty(cacheList)) {
                                long index = -1;
                                for (int i = 0; i < cacheList.size(); i++) {
                                    if (Objects.equals(cacheList.get(i).getId(), secondKillUsed.getId())) {
                                        index = i;
                                        break;
                                    }
                                }
                                if (index >= 0) {
                                    if (count >= secondKillUsed.getSeckillNum().longValue()) {
                                        secondKillUsed.setSeckillStatus(1);
                                        //先更新redis
                                        redisService.lset(key, index, secondKillUsed);
                                        SecondKill secondKillUpdate = new SecondKill();
                                        secondKillUpdate.setId(secondKillUsed.getId());
                                        secondKillUpdate.setSeckillStatus(1);
                                        secondKillService.updateSecondKillNoUpdateTime(secondKillUpdate);
                                    }
                                }
                            }
                            String orderKey = WendaoRedisKey.WENDAO_SECOND_KILL_FOR_ORDER_KEY + courseOrder.getOrderId();
                            redisService.setCacheObject(orderKey, "success");
                            //更新客户端信息
                            sendMns(courseOrder.getCourseId());
                        }
                        //将优惠券设置为已使用
                        ReceiveCouponInfo receiveCouponInfo = redisService.getCacheObject(REDIS_COUPON_KEY_PREFIX + orderId);
                        if (receiveCouponInfo != null && receiveCouponInfo.getReceiveCouponId() > 0) {
                            if (receiveCouponInfo.getType() == 0) {
                                MReceiveCoupon mReceiveCoupon = receiveCouponService.selectMReceiveCouponById(receiveCouponInfo.getReceiveCouponId());
                                mReceiveCoupon.setStatus(1);
                                receiveCouponService.updateMReceiveCoupon(mReceiveCoupon);
                            } else if (receiveCouponInfo.getType() == 1) {
                                receiveCouponService.updateMDiscountsCodeMap(receiveCouponInfo.getReceiveCouponId());
                            }
                        }
                        //创建流水
                        wendaoSettlementService.createFundsRecord(courseOrder,null);
                        return WxPayNotifyV3Response.success("支付回调处理成功");
                    } else {
                        //金额不等,订单不做处理
                        //返回失败
                        return WxPayNotifyV3Response.fail("支付金额不等于订单金额!");
                        //订单状态不变
                    }
                }
            }
        }
        return WxPayNotifyV3Response.fail("支付回调处理失败");
    }




    /**
     * 抖店触发领取课程
     * @param orderId
     * @return
     */
    @GetMapping("/genDoudianGiveOrder")
    public AjaxResult genDoudianGiveOrder(@RequestParam("orderId") String orderId) {
        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
        if (courseOrder == null) {
            return AjaxResult.error("订单不存在");
        }
        if (courseOrder.getOrderPlatform() != null && courseOrder.getOrderPlatform() == 11) {
            courseOrderService.giveUserCourses(courseOrder);
            return AjaxResult.success();
        }
        return AjaxResult.error("非抖店订单");
    }

    /**
     * 退款回调
     *
     * @param refundCallbackDTO
     * @return
     */
    @PostMapping("/ksRefundCallback")
    public RefundCallbackResult ksRefundCallback(@RequestBody RefundCallbackDTO refundCallbackDTO) {
        RefundCallbackResult result = new RefundCallbackResult();
        result.setResult(1);
        result.setMessage_id(refundCallbackDTO.getMessage_id());
        RefundData data = refundCallbackDTO.getData();
        String appId = refundCallbackDTO.getApp_id();
        if ("SUCCESS".equals(data.getStatus())) {
            System.out.println("退款成功");
            String refundId = data.getOut_refund_no();
            CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(refundId);
            if (courseRefund == null) {
                result.setResult(500);
                return result;
            }
            String orderId = courseRefund.getOrderId();
            courseRefund.setRefundStatus(1);
            courseRefund.setRefundType(1);
            String timeOutTimeValue = redisService.getCacheObject(WendaoRedisKey.refund_timeout_redis_key + courseRefund.getOrderId());
            if (StringUtils.isNotBlank(timeOutTimeValue)) {
                //超时退款
                courseRefund.setRefundType(2);
            }
            String platformRefund = redisService.getCacheObject(PLATFORM_REFUND_FROM_ZONGHOUTAI+courseRefund.getOrderId());
            if (StringUtils.isNotBlank(platformRefund)) {
                //平台退款
                courseRefund.setRefundType(6);
            }
            String complaintRefund = redisService.getCacheObject(PLATFORM_REFUND_FROM_Complaint+courseRefund.getOrderId());
            if (StringUtils.isNotBlank(complaintRefund)) {
                //用户投诉后退款
                courseRefund.setRefundType(9);
            }
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
            if (courseOrder == null) {
                result.setResult(500);
                return result;
            } else {
                System.out.println("订单不为空,设置订单为已退款");
                courseOrder.setOrderStatus(2);
                courseOrderService.updateCourseOrder(courseOrder);
            }
            courseRefundService.updateCourseRefund(courseRefund);
            //退款成功 修改资金状态 备注信息
            wendaoSettlementService.modifyFundsInfo(courseRefund, courseOrder);
            try {
                String accessToken = ksAccessTokenService.getKsAccessTokenByAppNameType(courseOrder.getAppNameType(), appId);
                syncKsOrder(courseOrder, data.getKs_order_no(), appId, accessToken, 6);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        // 退款失败的情况
        if ("FAILED".equals(data.getStatus())) {
            //退款退款失败
            String refundId = data.getOut_refund_no();
            CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(refundId);
            courseRefund.setRefundStatus(1);
            courseRefund.setRefundType(5);
            String orderId = courseRefund.getOrderId();
            courseRefundService.updateCourseRefund(courseRefund);
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
            try {
                String accessToken = ksAccessTokenService.getKsAccessTokenByAppNameType(courseOrder.getAppNameType(), appId);
                syncKsOrder(courseOrder, data.getKs_order_no(), appId, accessToken, 5);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }


    /**
     * 微信小程序退款回调
     *
     * @param request
     * @param response
     * @return
     */
    @PostMapping("/wxRefundCallback")
    public String wxRefundCallback(HttpServletRequest request, HttpServletResponse response) {
        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String serialNo = request.getHeader("Wechatpay-Serial");
        String signature = request.getHeader("Wechatpay-Signature");
        log.info("请求头参数为：timestamp:{} nonce:{} serialNo:{} signature:{}", timestamp, nonce, serialNo, signature);
        if (StringUtils.isAnyBlank(timestamp, nonce, serialNo, signature)) {
            return WxPayNotifyV3Response.fail("头部参数错误!");
        }
        WxPayRefundNotifyV3Result wxPayRefundNotifyV3Result = null;
        try {
            wxPayRefundNotifyV3Result = wxPayService.parseRefundNotifyV3Result(RequestUtils.readData(request),
                    new SignatureHeader(timestamp, nonce, signature, serialNo));
        } catch (WxPayException e) {
            log.error(e.getReturnMsg());
            return WxPayNotifyV3Response.fail("系统处理异常!");
        }
        if (wxPayRefundNotifyV3Result != null && wxPayRefundNotifyV3Result.getResult() != null) {
            WxPayRefundNotifyV3Result.DecryptNotifyResult result = wxPayRefundNotifyV3Result.getResult();
            if ("SUCCESS".equals(result.getRefundStatus())) {
                CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(result.getOutRefundNo());
                if (courseRefund == null) {
                    return WxPayNotifyV3Response.fail("退款回调处理失败:未找到退款单号:" + result.getOutRefundNo());
                }
                String orderId = courseRefund.getOrderId();
                courseRefund.setRefundStatus(1);
                courseRefund.setRefundType(1);
                String timeOutTimeValue = redisService.getCacheObject(WendaoRedisKey.refund_timeout_redis_key + courseRefund.getOrderId());
                if (StringUtils.isNotBlank(timeOutTimeValue)) {
                    //超时退款
                    courseRefund.setRefundType(2);
                }
                String platformRefund = redisService.getCacheObject(PLATFORM_REFUND_FROM_ZONGHOUTAI+courseRefund.getOrderId());
                if (StringUtils.isNotBlank(platformRefund)) {
                    //平台退款
                    courseRefund.setRefundType(6);
                }
                String complaintRefund = redisService.getCacheObject(PLATFORM_REFUND_FROM_Complaint+courseRefund.getOrderId());
                if (StringUtils.isNotBlank(complaintRefund)) {
                    //用户投诉后退款
                    courseRefund.setRefundType(9);
                }
                DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
                ZonedDateTime zonedDateTime = ZonedDateTime.parse(result.getSuccessTime(), formatter);
                // 转换为Date
                Date date = Date.from(zonedDateTime.toInstant());
                courseRefund.setRefundTime(date);
                CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
                if (courseOrder != null) {
                    System.out.println("订单不为空,设置订单为已退款");
                    courseOrder.setOrderStatus(2);
                    courseOrderService.updateCourseOrder(courseOrder);
                }
                courseRefundService.updateCourseRefund(courseRefund);
                //退款成功 修改资金状态 备注信息
                wendaoSettlementService.modifyFundsInfo(courseRefund, courseOrder);
                return WxPayNotifyV3Response.success("退款成功");
            } else {
                //退款失败
                String refundId = result.getOutRefundNo();
                CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(refundId);
                courseRefund.setRefundStatus(1);
                courseRefund.setRefundType(5);
                String orderId = courseRefund.getOrderId();
                courseRefundService.updateCourseRefund(courseRefund);
                return WxPayNotifyV3Response.success("退款失败处理成功");
            }
        }
        return WxPayNotifyV3Response.fail("退款回调处理失败");
    }

    @PostMapping("/alipayCallback")
    private String alipayCallback(HttpServletRequest request){
        Map<String,String> parameterMap = new HashMap<>();
        Map requestParams = request.getParameterMap();
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
            }
            parameterMap.put(name, valueStr);
        }
        String orderId = parameterMap.get("out_trade_no");
        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
        String tradeStatus = parameterMap.get("trade_status");
        boolean isSuccess = aliPayService.callbackVerify(parameterMap);
        if (isSuccess ) {
            //全额退款
            if(StringUtils.equals(tradeStatus, "TRADE_CLOSED")){
                String refundFee = parameterMap.get("refund_fee");
                if(StringUtils.isNotBlank(refundFee)){
                    int refundFeeInt = new BigDecimal(refundFee).multiply(new BigDecimal(100)).intValue();
                    int orderTotal = courseOrder.getPayPrice().multiply(new BigDecimal(100)).intValue();
                    if(refundFeeInt==orderTotal){
                        String outRefundNo = parameterMap.get("out_biz_no");

                        CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(outRefundNo);
                        if (courseRefund == null) {
                            return "fail";
                            //return WxPayNotifyV3Response.fail("退款回调处理失败:未找到退款单号:" + outRefundNo);
                        }
                        orderId = courseRefund.getOrderId();
                        courseRefund.setRefundStatus(1);
                        courseRefund.setRefundType(1);
                        String timeOutTimeValue = redisService.getCacheObject(WendaoRedisKey.refund_timeout_redis_key + courseRefund.getOrderId());
                        if (StringUtils.isNotBlank(timeOutTimeValue)) {
                            //超时退款
                            courseRefund.setRefundType(2);
                        }
                        String platformRefund = redisService.getCacheObject(PLATFORM_REFUND_FROM_ZONGHOUTAI+courseRefund.getOrderId());
                        if (StringUtils.isNotBlank(platformRefund)) {
                            //平台退款
                            courseRefund.setRefundType(6);
                        }
                        String complaintRefund = redisService.getCacheObject(PLATFORM_REFUND_FROM_Complaint+courseRefund.getOrderId());
                        if (StringUtils.isNotBlank(complaintRefund)) {
                            //用户投诉后退款
                            courseRefund.setRefundType(9);
                        }
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        try {
                            Date parse = sdf.parse(parameterMap.get("gmt_close"));
                            courseRefund.setRefundTime(parse);
                        } catch (ParseException e) {
                            System.out.println("格式化错误!错误信息:"+e.getMessage());
                        }
                        //CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
                        courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
                        if (courseOrder != null) {
                            System.out.println("订单不为空,设置订单为已退款");
                            courseOrder.setOrderStatus(2);
                            courseOrderService.updateCourseOrder(courseOrder);
                        }
                        courseRefundService.updateCourseRefund(courseRefund);
                        //退款成功 修改资金状态 备注信息
                        wendaoSettlementService.modifyFundsInfo(courseRefund, courseOrder);
                        return "success";
                    }else{
                        System.out.println("退款金额与订单金额不一致,订单号:"+orderId);
                        return "fail";
                    }
                }

            }
            if(StringUtils.equals(tradeStatus, "TRADE_SUCCESS")){
                //部分退也是trade_success
                String refundFee = parameterMap.get("refund_fee");
                if(StringUtils.isNotBlank(refundFee)){

                    //部分退款,暂时直接返回success

//                    int refundFeeInt = new BigDecimal(refundFee).multiply(new BigDecimal(100)).intValue();
//                    int orderTotal = courseOrder.getPayPrice().multiply(new BigDecimal(100)).intValue();
//                    if(refundFeeInt==orderTotal){
                    String outRefundNo = parameterMap.get("out_biz_no");

                    CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(outRefundNo);
                    if (courseRefund == null) {
                        return "fail";
                        //return WxPayNotifyV3Response.fail("退款回调处理失败:未找到退款单号:" + outRefundNo);
                    }
                    orderId = courseRefund.getOrderId();
                    courseRefund.setRefundStatus(1);
                    courseRefund.setRefundType(1);
                    String timeOutTimeValue = redisService.getCacheObject(WendaoRedisKey.refund_timeout_redis_key + courseRefund.getOrderId());
                    if (StringUtils.isNotBlank(timeOutTimeValue)) {
                        //超时退款
                        courseRefund.setRefundType(2);
                    }
                    String platformRefund = redisService.getCacheObject(PLATFORM_REFUND_FROM_ZONGHOUTAI+courseRefund.getOrderId());
                    if (StringUtils.isNotBlank(platformRefund)) {
                        //平台退款
                        courseRefund.setRefundType(6);
                    }
                    String complaintRefund = redisService.getCacheObject(PLATFORM_REFUND_FROM_Complaint+courseRefund.getOrderId());
                    if (StringUtils.isNotBlank(complaintRefund)) {
                        //用户投诉后退款
                        courseRefund.setRefundType(9);
                    }
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    try {
                        Date parse = sdf.parse(parameterMap.get("gmt_close"));
                        courseRefund.setRefundTime(parse);
                    } catch (ParseException e) {
                        System.out.println("格式化错误!错误信息:"+e.getMessage());
                    }
                    //CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
                    courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
                    if (courseOrder != null) {
                        System.out.println("订单不为空,设置订单为已退款");
                        courseOrder.setOrderStatus(2);
                        courseOrderService.updateCourseOrder(courseOrder);
                    }
                    courseRefundService.updateCourseRefund(courseRefund);
                    //退款成功 修改资金状态 备注信息
                    wendaoSettlementService.modifyFundsInfo(courseRefund, courseOrder);
                    return "success";
//                    }else{
//                        System.out.println("退款金额与订单金额不一致,订单号:"+orderId);
//                        return "fail";
//                    }
                    //return "success";
                }
                //是否已经支付
                if (courseOrder.getOrderStatus() != null && courseOrder.getOrderStatus() == 1) {
                    //已经支付
                    return "success";
                }
                String tradeNo = parameterMap.get("trade_no");
                String gmtPayment = parameterMap.get("gmt_payment");
                String totalAmount = parameterMap.get("total_amount");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    Date parse = sdf.parse(gmtPayment);
                    courseOrder.setPayTime(parse);
                } catch (ParseException e) {
                    System.out.println("格式化错误!错误信息:"+e.getMessage());
                }
                courseOrder.setPayWay("支付宝支付");
                courseOrder.setTradingOrderNumber(tradeNo);
                int total = new BigDecimal(totalAmount).multiply(new BigDecimal(100)).intValue();
                int orderTotal = courseOrder.getPayPrice().multiply(new BigDecimal(100)).intValue();
                if (total == orderTotal) {
                    courseOrder.setOrderStatus(1);
                    courseOrder.setSettleStatus(1);
                    courseOrderService.updateCourseOrder(courseOrder);
                    /**
                     * 秒杀逻辑
                     */
                    SecondKill secondKillUsed = redisService.getCacheObject(WendaoRedisKey.WENDAO_SECOND_KILL_KS_WX_ORDER_KEY + orderId);
                    if (secondKillUsed != null) {
                        //秒杀数量加1
                        long count = redisService.secKillIncr(secondKillUsed.getId());
                        //更新redis中的值
                        String key = WendaoRedisKey.WENDAO_SECOND_KILL_KEY + secondKillUsed.getCourseId();
                        List<SecondKill> cacheList = redisService.getCacheList(key);
                        if (CollectionUtils.isNotEmpty(cacheList)) {
                            long index = -1;
                            for (int i = 0; i < cacheList.size(); i++) {
                                if (Objects.equals(cacheList.get(i).getId(), secondKillUsed.getId())) {
                                    index = i;
                                    break;
                                }
                            }
                            if (index >= 0) {
                                if (count >= secondKillUsed.getSeckillNum().longValue()) {
                                    secondKillUsed.setSeckillStatus(1);
                                    //先更新redis
                                    redisService.lset(key, index, secondKillUsed);
                                    SecondKill secondKillUpdate = new SecondKill();
                                    secondKillUpdate.setId(secondKillUsed.getId());
                                    secondKillUpdate.setSeckillStatus(1);
                                    secondKillService.updateSecondKillNoUpdateTime(secondKillUpdate);
                                }
                            }
                        }
                        String orderKey = WendaoRedisKey.WENDAO_SECOND_KILL_FOR_ORDER_KEY + courseOrder.getOrderId();
                        if(StringUtils.isNotBlank(orderKey)){
                            redisService.setCacheObject(orderKey, "success");
                        }
                        //更新客户端信息
                        sendMns(courseOrder.getCourseId());
                    }
                    //将优惠券设置为已使用
                    ReceiveCouponInfo receiveCouponInfo = redisService.getCacheObject(REDIS_COUPON_KEY_PREFIX + orderId);
                    if (receiveCouponInfo != null && receiveCouponInfo.getReceiveCouponId() > 0) {
                        if (receiveCouponInfo.getType() == 0) {
                            MReceiveCoupon mReceiveCoupon = receiveCouponService.selectMReceiveCouponById(receiveCouponInfo.getReceiveCouponId());
                            mReceiveCoupon.setStatus(1);
                            receiveCouponService.updateMReceiveCoupon(mReceiveCoupon);
                        } else if (receiveCouponInfo.getType() == 1) {
                            receiveCouponService.updateMDiscountsCodeMap(receiveCouponInfo.getReceiveCouponId());
                        }
                    }
                    //创建流水
                    wendaoSettlementService.createFundsRecord(courseOrder,null);
                    /**
                     * 发送抖音的短信
                     */
                    String redis2Key = apple_dy_send_sms0_key_prefix + courseOrder.getBuyerUserId() + "_" + courseOrder.getCourseId();
                    String cacheResult = redisService.getCacheObject(redis2Key);
                    if (StringUtils.isNotBlank(cacheResult)) {
                        if (courseOrder.getOrderPlatform() == 0) {
                            String clientTokenKey = dyConfig.getClientAccessTokenKey() + ":" + courseOrder.getAppNameType();
                            String clentAccessToken = redisService.getCacheObject(clientTokenKey);
                            String link = redisService.getCacheObject(dy_order_code_url + courseOrder.getCourseId());
                            if (StringUtils.isBlank(link) && StringUtils.isNotBlank(clentAccessToken)) {
                                GenerateUrlLinkDTO generateUrlLinkDTO = new GenerateUrlLinkDTO();
                                generateUrlLinkDTO.setApp_id(Appids.getAppidByAppNameType(courseOrder.getAppNameType()));
                                generateUrlLinkDTO.setApp_name("douyin");
                                //加160天
                                long time = (new Date().getTime() + 160L * 24L * 60L * 60L * 1000L) / 1000;
                                generateUrlLinkDTO.setExpire_time(time);
                                generateUrlLinkDTO.setQuery("{\"course_id\":\"" + courseOrder.getCourseId() + "\"}");
                                generateUrlLinkDTO.setPath("pages_details/details/details");
                                GenerateUrlLinkResult generateUrlLinkResult = postRequest(generateUrlLinkDTO, "/api/apps/v1/url_link/generate/", GenerateUrlLinkResult.class, "https://open.douyin.com", clentAccessToken);
                                if (generateUrlLinkResult != null && generateUrlLinkResult.getErr_no() == 0) {
                                    redisService.setCacheObject(dy_order_code_url + courseOrder.getCourseId(), generateUrlLinkResult.getData().getUrl_link(), 160L, TimeUnit.DAYS);
                                    sendAppleGuidSmsDy0(courseOrder.getBuyerUserMobile(), courseOrder.getCourseTitle(), String.valueOf(courseOrder.getCourseId()));
                                }
                            } else {
                                if (StringUtils.isNotBlank(link)) {
                                    sendAppleGuidSmsDy0(courseOrder.getBuyerUserMobile(), courseOrder.getCourseTitle(), String.valueOf(courseOrder.getCourseId()));
                                }
                            }
                        }
                        if (courseOrder.getOrderPlatform() == 2) {
                            //发送快手链接
                            String appid = null;
                            Integer appNameType = courseOrder.getAppNameType();
                            if(appNameType==1){
                                appid = kuaishouConfig.getAppid();
                            }
                            if(appNameType==2){
                                appid = zkKuaishouConfig.getAppid();
                            }
                            if (StringUtils.isNotBlank(appid)) {
                                String link = redisService.getCacheObject(ks_order_code_url + courseOrder.getCourseId());
                                if (StringUtils.isBlank(link)) {
                                    link = "kwai://miniapp?appId=" + appid +
                                            "&KSMP_source=011005&KSMP_internal_source=011005&path=pages_details%2Fdetails%2Fdetails%3Fid%3D" +
                                            courseOrder.getCourseId() + "%26platform%3D2%26appNameType%3D" + courseOrder.getAppNameType();
                                    redisService.setCacheObject(ks_order_code_url + courseOrder.getCourseId(), link, 160L, TimeUnit.DAYS);
                                }
                                sendAppleGuidSmsKs0(courseOrder.getBuyerUserMobile(), courseOrder.getCourseTitle(), String.valueOf(courseOrder.getCourseId()));
                            }
                        }
                    }
                    return "success";
                } else {
                    System.out.println("订单金额和支付金额不相等,订单号:"+orderId);
                    return "fail";
                }
            }
        }else{
            System.out.println("验签失败!");
            return "fail";
        }
        return "fail";
    }

    private void sendAppleGuidSmsKs0(String phoneNumber, String courseName, String wxcode){
        try {
            StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                    .accessKeyId("LTAI5tRfCNfcpvFCRaM1ydiy")
                    .accessKeySecret("******************************")
                    .build());

            AsyncClient client = AsyncClient.builder()
                    .region("cn-hangzhou") // Region ID
                    .credentialsProvider(provider)
                    .overrideConfiguration(
                            ClientOverrideConfiguration.create()
                                    .setEndpointOverride("dysmsapi.aliyuncs.com")
                    )
                    .build();

            SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                    .signName("问到")
                    .templateCode("SMS_467415197")
                    .phoneNumbers(phoneNumber)
                    .templateParam("{\"courseName\":\""+courseName+"\",\"wxcode\":\""+wxcode+"\"}")
                    .build();

            CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
            SendSmsResponse resp = response.get();
            System.out.println(new Gson().toJson(resp));
            client.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void sendAppleGuidSmsDy0(String phoneNumber, String courseName, String wxcode){
        try {
            StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                    .accessKeyId("LTAI5tRfCNfcpvFCRaM1ydiy")
                    .accessKeySecret("******************************")
                    .build());

            AsyncClient client = AsyncClient.builder()
                    .region("cn-hangzhou") // Region ID
                    .credentialsProvider(provider)
                    .overrideConfiguration(
                            ClientOverrideConfiguration.create()
                                    .setEndpointOverride("dysmsapi.aliyuncs.com")
                    )
                    .build();

            SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                    .signName("问到")
                    .templateCode("SMS_467595113")
                    .phoneNumbers(phoneNumber)
                    .templateParam("{\"courseName\":\""+courseName+"\",\"wxcode\":\""+wxcode+"\"}")
                    .build();

            CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
            SendSmsResponse resp = response.get();
            System.out.println(new Gson().toJson(resp));
            client.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private <T, K> T postRequest(K dto, String uri, Class<T> clazz,String domain,String clentAccessToken) {
        String url = domain + uri;
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String body = JSON.toJSONString(dto);

        HttpPost httpPost = new HttpPost(url);
        try {
            httpPost.addHeader("Content-Type", "application/json");
            httpPost.addHeader("access-token", clentAccessToken);
            StringEntity stringEntity = new StringEntity(body, "utf-8");
            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String content = EntityUtils.toString(entity, "UTF-8");
                return JSON.parseObject(content, clazz);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return null;
    }

    private void syncKsOrder(CourseOrder courseOrder, String outOrderNumber, String appId, String accessToken, Integer orderStatus) {
        String imgId = null;
        KsOrderReport ksOrderReportQuery = orderReportService.selectByOutBizOrderNo(outOrderNumber);
        if (ksOrderReportQuery != null) {
            imgId = ksOrderReportQuery.getProductCoverImgId();
        }
        ReportDTO reportDTO = new ReportDTO();
        reportDTO.setOut_biz_order_no(outOrderNumber);
        reportDTO.setOut_order_no(courseOrder.getOrderId());
        Long buyerUserId = courseOrder.getBuyerUserId();

        WendaoUser wenDaoUserByUserId = wenDaoUserService.getWenDaoUserByUserId(buyerUserId);

        reportDTO.setOpen_id(wenDaoUserByUserId.getOpenId());
        reportDTO.setOrder_create_time(courseOrder.getOrderTime().getTime()-1000L);
        reportDTO.setOrder_status(orderStatus);
        reportDTO.setOrder_path("/pages_mine/order_details/order_details?id=" + courseOrder.getId());
        //图片处理
        if (StringUtils.isBlank(imgId)) {
            String courseImgUrl = courseOrder.getCourseImgUrl();
            //对图片进行1:1处理
            String url = null;
            if (StringUtils.isNotBlank(courseImgUrl) && courseImgUrl.contains("vod-qcloud.com")) {
                url = courseOrder.getCourseImgUrl() + "!40.png";
            } else {
                url = upload(courseImgUrl) + "!40.png";
            }
            //上传商品图片
            UploadWithUrlResult uploadWithUrlResult = kuaishouPayService.uploadWithUrl(appId, accessToken, url);
            imgId = uploadWithUrlResult.getData().getImgId();

        }
        reportDTO.setProduct_cover_img_id(imgId);
        //
        ReportResult report = kuaishouPayService.report(appId, accessToken, reportDTO);
        if (report.getResult() == 1) {
            KsOrderReport ksOrderReport = new KsOrderReport();
            ksOrderReport.setOutBizOrderNo(reportDTO.getOut_biz_order_no());
            ksOrderReport.setOutOrderNo(reportDTO.getOut_order_no());
            ksOrderReport.setOpenId(reportDTO.getOpen_id());
            ksOrderReport.setOrderCreateTime(courseOrder.getOrderTime());
            ksOrderReport.setOrderStatus(reportDTO.getOrder_status());
            ksOrderReport.setOrderPath(reportDTO.getOrder_path());
            ksOrderReport.setProductCoverImgId(reportDTO.getProduct_cover_img_id());
            orderReportService.insertKsOrderReport(ksOrderReport);
            System.out.println("订单号:" + courseOrder.getOrderId() + "同步成功!");
        } else {
            System.out.println("订单号:" + courseOrder.getOrderId() + "同步失败!返回信息为:" + JSON.toJSONString(report));
        }
    }

    private String upload(String urlStr) {
        try {
            VodUploadClient client = new VodUploadClient(secretId, secretKey);
            VodUploadRequest request = new VodUploadRequest();
            URL url = new URL(urlStr);
            /**
             * fileName
             */
            String fileName = UUID.randomUUID().toString();
            String extension = FilenameUtils.getExtension(urlStr);
            String filePath = "/root/temp_upload/" + fileName + "." + extension;
            File file = new File(filePath);
            FileUtils.copyURLToFile(url, file);
            request.setMediaFilePath(filePath);
            request.setSubAppId(vodsubAppid);
            request.setStorageRegion("ap-chongqing");
            VodUploadResponse response = client.upload("ap-chongqing", request);
            file.delete();
            log.info("Upload FileId = {}", response.getFileId());
            System.out.println("内容:" + JSON.toJSONString(response));
            return response.getMediaUrl();
        } catch (Exception e) {
            // 业务方进行异常处理
            log.error("Upload Err", e);
        }
        return null;
    }

    private void sendMns(Long courseId) {
        CloudTopic topic = mnsClient.getTopicRef("second-kill-topic");
        try {
            TopicMessage msg = new RawTopicMessage(); //可以使用TopicMessage结构，选择不进行Base64加密。
            msg.setMessageBody(String.valueOf(courseId));
            msg.setMessageTag("secondKill"); //设置该条发布消息的filterTag。
            msg = topic.publishMessage(msg);
            System.out.println(msg.getMessageId());
            System.out.println(msg.getMessageBodyMD5());
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("subscribe error");
        }
    }
}
