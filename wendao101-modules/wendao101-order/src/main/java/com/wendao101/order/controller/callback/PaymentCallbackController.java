package com.wendao101.order.controller.callback;

import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.kuaishou.pay.callback.PayCallbackResult;
import com.wendao101.order.service.callback.IPaymentCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 支付回调处理Controller
 * 统一处理各平台的支付回调
 */
@Slf4j
@RestController
@RequestMapping("/payment/callback")
public class PaymentCallbackController {

    @Autowired
    private IPaymentCallbackService paymentCallbackService;

    /**
     * 微信支付回调
     */
    @PostMapping("/wx/pay")
    public String wxPayCallback(HttpServletRequest request, HttpServletResponse response) {
        log.info("收到微信支付回调");
        return paymentCallbackService.handleWxPayCallback(request, response);
    }

    /**
     * 微信退款回调
     */
    @PostMapping("/wx/refund")
    public String wxRefundCallback(HttpServletRequest request, HttpServletResponse response) {
        log.info("收到微信退款回调");
        return paymentCallbackService.handleWxRefundCallback(request, response);
    }

    /**
     * 快手支付回调
     */
    @PostMapping("/ks/pay")
    public PayCallbackResult kuaishouPayCallback(HttpServletRequest request) {
        log.info("收到快手支付回调");
        return paymentCallbackService.handleKuaishouPayCallback(request);
    }

    /**
     * 支付宝支付回调
     */
    @PostMapping("/alipay")
    public String alipayCallback(HttpServletRequest request) {
        log.info("收到支付宝支付回调");
        return paymentCallbackService.handleAlipayCallback(request);
    }

    /**
     * 抖店回调
     */
    @PostMapping("/doudian")
    public AjaxResult doudianCallback(HttpServletRequest request) {
        log.info("收到抖店回调");
        return paymentCallbackService.handleDoudianCallback(request);
    }

    /**
     * 抖店短信回调
     */
    @PostMapping("/doudian/sms")
    public AjaxResult doudianSmsCallback(HttpServletRequest request) {
        log.info("收到抖店短信回调");
        return paymentCallbackService.handleDoudianSmsCallback(request);
    }

    /**
     * 小红书回调
     */
    @PostMapping("/xhs")
    public AjaxResult xhsCallback(HttpServletRequest request) {
        log.info("收到小红书回调");
        return paymentCallbackService.handleXhsCallback(request);
    }
}