<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.ShopGotowxWhitelistMapper">

    <resultMap type="ShopGotowxWhitelist" id="ShopGotowxWhitelistResult">
        <result property="id" column="id"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="mobile" column="mobile"/>
        <result property="shopName" column="shop_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="wxMiniappOpenStatus" column="wx_miniapp_open_status"/>
        <result property="douyinMiniappOpenStatus" column="douyin_miniapp_open_status"/>
        <result property="teacherName" column="teacher_name"/>
        <result property="appNameType" column="app_name_type"/>
    </resultMap>

    <sql id="selectShopGotowxWhitelistVo">
        select id, teacher_id, mobile, shop_name, create_time, update_time, wx_miniapp_open_status, douyin_miniapp_open_status, teacher_name, app_name_type
        from shop_gotowx_whitelist
    </sql>

    <select id="selectShopGotowxWhitelistList" parameterType="ShopGotowxWhitelist" resultMap="ShopGotowxWhitelistResult">
        <include refid="selectShopGotowxWhitelistVo"/>
        <where>
            <if test="teacherId != null">and teacher_id = #{teacherId}</if>
            <if test="mobile != null and mobile != ''">and mobile = #{mobile}</if>
            <if test="shopName != null and shopName != ''">and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="teacherName != null and teacherName != ''">and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="wxMiniappOpenStatus != null">and wx_miniapp_open_status = #{wxMiniappOpenStatus}</if>
            <if test="douyinMiniappOpenStatus != null">and douyin_miniapp_open_status = #{douyinMiniappOpenStatus}</if>
            <if test="appNameType != null">and app_name_type = #{appNameType}</if>
            <if test="teacherInfo != null  and teacherInfo != ''">
                and (
                teacher_id = #{teacherInfo}
                or teacher_name like concat('%', #{teacherInfo}, '%')
                or mobile = #{teacherInfo}
                or shop_name like concat('%', #{teacherInfo}, '%')
                )
            </if>
        </where>
        ORDER BY create_time desc
    </select>

    <select id="selectShopGotowxWhitelistById" parameterType="Long" resultMap="ShopGotowxWhitelistResult">
        <include refid="selectShopGotowxWhitelistVo"/>
        where id = #{id}
    </select>

    <select id="selectShopGotowxWhitelistByTeacherId" resultMap="ShopGotowxWhitelistResult">
        <include refid="selectShopGotowxWhitelistVo"/>
        where teacher_id = #{teacherId} limit 1
    </select>

    <insert id="insertShopGotowxWhitelist" parameterType="ShopGotowxWhitelist" useGeneratedKeys="true" keyProperty="id">
        insert into shop_gotowx_whitelist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="shopName != null and shopName != ''">shop_name,</if>
            <if test="wxMiniappOpenStatus != null">wx_miniapp_open_status,</if>
            <if test="douyinMiniappOpenStatus != null">douyin_miniapp_open_status,</if>
            <if test="teacherName != null and teacherName != ''">teacher_name,</if>
            <if test="appNameType != null">app_name_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="shopName != null and shopName != ''">#{shopName},</if>
            <if test="wxMiniappOpenStatus != null">#{wxMiniappOpenStatus},</if>
            <if test="douyinMiniappOpenStatus != null">#{douyinMiniappOpenStatus},</if>
            <if test="teacherName != null and teacherName != ''">#{teacherName},</if>
            <if test="appNameType != null">#{appNameType},</if>
        </trim>
    </insert>

    <update id="updateShopGotowxWhitelist" parameterType="ShopGotowxWhitelist">
        update shop_gotowx_whitelist
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="shopName != null and shopName != ''">shop_name = #{shopName},</if>
            <if test="wxMiniappOpenStatus != null">wx_miniapp_open_status = #{wxMiniappOpenStatus},</if>
            <if test="douyinMiniappOpenStatus != null">douyin_miniapp_open_status = #{douyinMiniappOpenStatus},</if>
            <if test="teacherName != null and teacherName != ''">teacher_name = #{teacherName},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShopGotowxWhitelistById" parameterType="Long">
        delete from shop_gotowx_whitelist where id = #{id}
    </delete>

    <delete id="deleteShopGotowxWhitelistByIds" parameterType="String">
        delete from shop_gotowx_whitelist where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
