<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.TeacherStatisticsMapper">
    
    <resultMap type="TeacherStatistics" id="TeacherStatisticsResult">
        <result property="id"    column="id"    />
        <result property="statisticsDate"    column="statistics_date"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="mobile"    column="mobile"    />
        <result property="shopName"    column="shop_name"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="dealAmount"    column="deal_amount"    />
        <result property="withdrawnAmount"    column="withdrawn_amount"    />
        <result property="moneyInTransit"    column="money_in_transit"    />
        <result property="withdrawableAmount"    column="withdrawable_amount"    />
        <result property="serviceFee"    column="service_fee"    />
        <result property="serviceFeeRate"    column="service_fee_rate"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
	
	<resultMap type="SysDept" id="SysDeptResult">
		<result property="deptId"    column="dept_id"    />
		<result property="parentId"    column="parent_id"    />
		<result property="deptName"    column="dept_name"    />
	</resultMap>
	
    <sql id="selectTeacherStatisticsVo">
        select id, statistics_date, teacher_id, mobile, shop_name, app_name_type, deal_amount, withdrawn_amount, money_in_transit, withdrawable_amount, service_fee, service_fee_rate, create_time, update_time 
        from teacher_statistics
    </sql>

    <select id="selectTeacherStatisticsList" parameterType="TeacherStatistics" resultMap="TeacherStatisticsResult">
        <include refid="selectTeacherStatisticsVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
            <if test="teacherInfo != null  and teacherInfo != ''"> and (shop_name like concat('%', #{teacherInfo}, '%') or mobile = #{teacherInfo} or teacher_id = #{teacherInfo}) </if>
            <choose>
                 <when test="timeQueryStr == null or timeQueryStr==''">
                     and time_query_str is null
                 </when>
                 <otherwise>
                     and time_query_str = #{timeQueryStr}
                 </otherwise>
             </choose>
        </where>
    </select>
    
    <select id="selectTeacherStatisticsById" parameterType="Long" resultMap="TeacherStatisticsResult">
        <include refid="selectTeacherStatisticsVo"/>
        where id = #{id}
    </select>
    <select id="selectTeacherStatisticsSum"
            resultType="com.wendao101.common.core.web.page.TableDataInfoWithSumSaleMoney">
        select sum(deal_amount) as totalDealAmount
             , sum(withdrawn_amount) as totalWithdrawnAmount
             , sum(money_in_transit) as totalMoneyInTransit
             , sum(withdrawable_amount) as totalWithdrawableAmount
             , sum(service_fee) as totalServiceFee
        from teacher_statistics
        <where>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
            <if test="teacherInfo != null  and teacherInfo != ''"> and (shop_name like concat('%', #{teacherInfo}, '%') or mobile = #{teacherInfo} or teacher_id = #{teacherInfo}) </if>

            <choose>
                <when test="timeQueryStr == null or timeQueryStr==''">
                    and time_query_str is null
                </when>
                <otherwise>
                    and time_query_str = #{timeQueryStr}
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="selectPromotersByTeacherId" resultType="java.lang.Long">
        select id from `wendao101-teacher`.promoter where teacher_id = #{teacherId}
    </select>
    <select id="selectEmployeeStatisticsList" parameterType="TeacherStatistics" resultType="com.wendao101.order.domain.EmployeeStatistics">
        SELECT
        id,
        deal_amount as dealAmount,
        withdrawn_amount as withdrawnAmount,
        money_in_transit as moneyInTransit,
        withdrawable_amount as withdrawableAmount,
        create_time as createTime,
        update_time as updateTime,
        time_query_str as timeQueryStr,
        withdrawn_amount_fee as withdrawnAmountFee,
        not_withdrawn_fee as notWithdrawnFee,
        employee_id as employeeId,
        nick_name as nickName,
        total_withdrawn_fee as totalWithdrawnFee,
        withdrawn_net_commission as withdrawnNetCommission,
        not_withdrawn_net_commission as notWithdrawnNetCommission,
        total_withdrawn_net_commission as totalWithdrawnNetCommission
        FROM
        employee_statistics
            <where>
                <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
                <if test="userIdList != null and userIdList.size() > 0">
                    and employee_id in
                    <foreach item="userId" collection="userIdList" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                </if>
                <choose>
                    <when test="timeQueryStr == null or timeQueryStr==''">
                        and time_query_str is null
                    </when>
                    <otherwise>
                        and time_query_str = #{timeQueryStr}
                    </otherwise>
                </choose>
            </where>
    </select>

    <insert id="insertTeacherStatistics" parameterType="TeacherStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into teacher_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statisticsDate != null">statistics_date,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="mobile != null">mobile,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="dealAmount != null">deal_amount,</if>
            <if test="withdrawnAmount != null">withdrawn_amount,</if>
            <if test="moneyInTransit != null">money_in_transit,</if>
            <if test="withdrawableAmount != null">withdrawable_amount,</if>
            <if test="serviceFee != null">service_fee,</if>
            <if test="serviceFeeRate != null">service_fee_rate,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="statisticsDate != null">#{statisticsDate},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="dealAmount != null">#{dealAmount},</if>
            <if test="withdrawnAmount != null">#{withdrawnAmount},</if>
            <if test="moneyInTransit != null">#{moneyInTransit},</if>
            <if test="withdrawableAmount != null">#{withdrawableAmount},</if>
            <if test="serviceFee != null">#{serviceFee},</if>
            <if test="serviceFeeRate != null">#{serviceFeeRate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTeacherStatistics" parameterType="TeacherStatistics">
        update teacher_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="statisticsDate != null">statistics_date = #{statisticsDate},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="dealAmount != null">deal_amount = #{dealAmount},</if>
            <if test="withdrawnAmount != null">withdrawn_amount = #{withdrawnAmount},</if>
            <if test="moneyInTransit != null">money_in_transit = #{moneyInTransit},</if>
            <if test="withdrawableAmount != null">withdrawable_amount = #{withdrawableAmount},</if>
            <if test="serviceFee != null">service_fee = #{serviceFee},</if>
            <if test="serviceFeeRate != null">service_fee_rate = #{serviceFeeRate},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherStatisticsById" parameterType="Long">
        delete from teacher_statistics where id = #{id}
    </delete>

    <delete id="deleteTeacherStatisticsByIds" parameterType="String">
        delete from teacher_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectEmployeeStatisticsSum"
            resultType="com.wendao101.common.core.web.page.TableDataInfoWithEmployeeDataSum">
        select sum(deal_amount) as dealAmount
             , sum(withdrawn_amount) as withdrawnAmount
             , sum(money_in_transit) as moneyInTransit
             , sum(withdrawable_amount) as withdrawableAmount
             , sum(withdrawn_amount_fee) as withdrawnAmountFee
             , sum(not_withdrawn_fee) as notWithdrawnFee
             , sum(total_withdrawn_fee) as totalWithdrawnFee
             , sum(withdrawn_net_commission) as withdrawnNetCommission
             , sum(not_withdrawn_net_commission) as notWithdrawnNetCommission
             , sum(total_withdrawn_net_commission) as totalWithdrawnNetCommission
        from employee_statistics
        <where>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="userIdList != null and userIdList.size() > 0">
                and employee_id in
                <foreach item="userId" collection="userIdList" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <choose>
                <when test="timeQueryStr == null or timeQueryStr==''">
                    and time_query_str is null
                </when>
                <otherwise>
                    and time_query_str = #{timeQueryStr}
                </otherwise>
            </choose>
        </where>
    </select>
	
	<select id="selectDeptByEmployeeId" parameterType="Long" resultMap="SysDeptResult">
		select b.dept_id,b.parent_id,b.dept_name 
		from `ry`.`sys_user` a 
		left join `ry`.`sys_dept` b on a.dept_id=b.dept_id 
		where a.dept_id is not null and a.user_id=#{employeeId} 
		limit 1
	</select>
	
	<select id="selectParentDeptById" parameterType="Long" resultMap="SysDeptResult">
		select dept_id,parent_id,dept_name 
		from `ry`.`sys_dept` 
		where dept_id=#{deptId}
	</select>
</mapper>